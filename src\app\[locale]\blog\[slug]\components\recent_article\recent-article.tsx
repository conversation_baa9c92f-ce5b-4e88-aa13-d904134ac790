import AppImage from '@/components/ui/app_image';
import { cn } from '@/lib/utils';
import { ImageData } from '@/models/ImageData';
import { formatDate } from 'date-fns';
import Link from 'next/link';

interface IRecentArticle {
  title: string;
  createdAt: Date;
  id: number;
  image?: ImageData;
  slug: string;
  isLast?: boolean;
}

function RecentArticle({
  title,
  createdAt,
  id,
  image,
  slug,
  isLast,
}: IRecentArticle) {
  return (
    <Link
      href={`/blog/${slug}`}
      className={cn(
        'w-full flex flex-row gap-2 items-start group  ',
        !isLast && 'xl:border-b xl:border-b-slate-300 xl:pb-4 xl:border-dashed',
      )}
    >
      <div className="h-full rounded-full  min-w-[60px]  max-w-[60px] min-h-[60px] max-h-[60px] relative overflow-hidden ">
        <AppImage
          fill={true}
          image={image}
          className="object-cover h-full w-full   "
        />
      </div>

      <div className="w-full flex flex-col gap-1   ">
        <h3 className="text-sm  text-foreground/80  line-clamp-2 overflow-hidden text-ellipsis w-full  group-hover:text-secondary transition-colors duration-300 ">
          {title}
        </h3>
        <p className=" text-[10px] text-secondary tracking-widest line-clamp-2 overflow-hidden text-ellipsis w-full ">
          {createdAt && formatDate(createdAt, 'dd/MM/yyyy')}
        </p>
      </div>
    </Link>
  );
}

export default RecentArticle;
