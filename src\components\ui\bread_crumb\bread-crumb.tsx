import { cn } from '@/lib/utils';
import { ChevronRightIcon } from '@radix-ui/react-icons';
import Link from 'next/link';
import React from 'react';
import { Button } from '../button';
import { FiHome } from 'react-icons/fi';

export type BreadCrumbType = {
  title: string;
  link?: string;
};

type BreadCrumbPropsType = {
  items: BreadCrumbType[];
};

export default function BreadCrumb({ items }: BreadCrumbPropsType) {
  return (
    <div className=" flex flex-row  items-center space-x-1 text-sm text-gray-500 uppercase">
      <Link
        href={'/'}
        className="overflow-hidden text-ellipsis whitespace-nowrap"
      >
        <Button variant="transparent" className="p-0">
          <FiHome className="size-4  text-gray-500  " />
        </Button>
      </Link>
      {items?.map((item: BreadCrumbType, index: number) => (
        <React.Fragment key={item.title}>
          <ChevronRightIcon className="h-4 w-4" />
          {item.link ? (
            <Link
              href={item.link}
              className={cn(
                'font-medium',
                index === 0 ? 'text-gray-500' : 'text-gray-500',
              )}
            >
              {item.title}
            </Link>
          ) : (
            <span className="font-medium">{item.title}</span>
          )}
        </React.Fragment>
      ))}
    </div>
  );
}
