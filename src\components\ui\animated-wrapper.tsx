'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface AnimatedWrapperProps {
  children: ReactNode;
  variants?: any;
  className?: string;
  delay?: number;
  duration?: number;
  once?: boolean;
  margin?: string;
  amount?: number;
}

export default function AnimatedWrapper({
  children,
  variants,
  className = '',
  delay = 0,
  duration = 0.6,
  once = true,
  margin = '-100px',
  amount = 0.3,
}: AnimatedWrapperProps) {
  const defaultVariants = {
    hidden: {
      opacity: 0,
      y: 50,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration,
        ease: 'easeOut',
        delay,
      },
    },
  };

  return (
    <motion.div
      className={className}
      variants={variants || defaultVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once, margin, amount }}
    >
      {children}
    </motion.div>
  );
}
