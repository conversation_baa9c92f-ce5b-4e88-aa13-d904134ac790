'use client';
import { useEffect, useRef, useState } from 'react';

interface MapMarker {
  id: string;
  name: string;
  lat: number;
  lng: number;
  type?: 'primary' | 'secondary';
}

interface GoogleMapProps {
  markers?: MapMarker[];
  center?: { lat: number; lng: number };
  zoom?: number;
  className?: string;
  height?: string;
}

// Canadian cities coordinates
const defaultMarkers: MapMarker[] = [
  // Alberta
  {
    id: 'calgary',
    name: 'Calgary',
    lat: 51.0447,
    lng: -114.0719,
    type: 'primary',
  },
  {
    id: 'edmonton',
    name: 'Edmonton',
    lat: 53.5461,
    lng: -113.4938,
    type: 'primary',
  },
  {
    id: 'red-deer',
    name: 'Red Deer',
    lat: 52.2681,
    lng: -113.8112,
    type: 'secondary',
  },

  // British Columbia
  {
    id: 'vancouver',
    name: 'Vancouver',
    lat: 49.2827,
    lng: -123.1207,
    type: 'primary',
  },
  {
    id: 'kelowna',
    name: '<PERSON><PERSON><PERSON>',
    lat: 49.888,
    lng: -119.496,
    type: 'secondary',
  },
  {
    id: 'victoria',
    name: 'Victoria',
    lat: 48.4284,
    lng: -123.3656,
    type: 'secondary',
  },

  // Other regions
  {
    id: 'regina',
    name: 'Regina',
    lat: 50.4452,
    lng: -104.6189,
    type: 'secondary',
  },
  {
    id: 'saskatoon',
    name: 'Saskatoon',
    lat: 52.1579,
    lng: -106.6702,
    type: 'secondary',
  },
  {
    id: 'winnipeg',
    name: 'Winnipeg',
    lat: 49.8951,
    lng: -97.1384,
    type: 'secondary',
  },
];

export default function GoogleMap({
  markers = defaultMarkers,
  center = { lat: 52.5, lng: -110 }, // Center of Canada (Alberta/Saskatchewan border)
  zoom = 5,
  className = '',
  height = '400px',
}: GoogleMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load Google Maps script
  useEffect(() => {
    if (typeof window !== 'undefined' && !window.google) {
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_Google_API_Key}&libraries=places`;
      script.async = true;
      script.defer = true;
      script.onload = () => setIsLoaded(true);
      document.head.appendChild(script);
    } else if (window.google) {
      setIsLoaded(true);
    }
  }, []);

  // Initialize map
  useEffect(() => {
    if (isLoaded && mapRef.current && !map) {
      const newMap = new google.maps.Map(mapRef.current, {
        center,
        zoom,
        styles: [
          {
            featureType: 'water',
            elementType: 'geometry',
            stylers: [{ color: '#e9e9e9' }, { lightness: 17 }],
          },
          {
            featureType: 'landscape',
            elementType: 'geometry',
            stylers: [{ color: '#f5f5f5' }, { lightness: 20 }],
          },
          {
            featureType: 'road.highway',
            elementType: 'geometry.fill',
            stylers: [{ color: '#ffffff' }, { lightness: 17 }],
          },
          {
            featureType: 'road.highway',
            elementType: 'geometry.stroke',
            stylers: [{ color: '#ffffff' }, { lightness: 29 }, { weight: 0.2 }],
          },
          {
            featureType: 'road.arterial',
            elementType: 'geometry',
            stylers: [{ color: '#ffffff' }, { lightness: 18 }],
          },
          {
            featureType: 'road.local',
            elementType: 'geometry',
            stylers: [{ color: '#ffffff' }, { lightness: 16 }],
          },
          {
            featureType: 'poi',
            elementType: 'geometry',
            stylers: [{ color: '#f5f5f5' }, { lightness: 21 }],
          },
          {
            featureType: 'poi.park',
            elementType: 'geometry',
            stylers: [{ color: '#dedede' }, { lightness: 21 }],
          },
          {
            elementType: 'labels.text.stroke',
            stylers: [
              { visibility: 'on' },
              { color: '#ffffff' },
              { lightness: 16 },
            ],
          },
          {
            elementType: 'labels.text.fill',
            stylers: [
              { saturation: 36 },
              { color: '#333333' },
              { lightness: 40 },
            ],
          },
          {
            elementType: 'labels.icon',
            stylers: [{ visibility: 'off' }],
          },
          {
            featureType: 'transit',
            elementType: 'geometry',
            stylers: [{ color: '#f2f2f2' }, { lightness: 19 }],
          },
          {
            featureType: 'administrative',
            elementType: 'geometry.fill',
            stylers: [{ color: '#fefefe' }, { lightness: 20 }],
          },
          {
            featureType: 'administrative',
            elementType: 'geometry.stroke',
            stylers: [{ color: '#fefefe' }, { lightness: 17 }, { weight: 1.2 }],
          },
        ],
        disableDefaultUI: true,
        zoomControl: true,
        scrollwheel: false,
        gestureHandling: 'cooperative',
      });

      setMap(newMap);
    }
  }, [isLoaded, center, zoom, map]);

  // Add markers
  useEffect(() => {
    if (map && markers.length > 0) {
      markers.forEach((marker) => {
        const mapMarker = new google.maps.Marker({
          position: { lat: marker.lat, lng: marker.lng },
          map,
          title: marker.name,
          icon: {
            path: google.maps.SymbolPath.CIRCLE,
            scale: marker.type === 'primary' ? 12 : 8,
            fillColor: marker.type === 'primary' ? '#00646C' : '#B10055',
            fillOpacity: 1,
            strokeColor: '#ffffff',
            strokeWeight: 2,
          },
        });

        const infoWindow = new google.maps.InfoWindow({
          content: `<div style="padding: 8px; font-weight: 500; color: #333;">${marker.name}</div>`,
        });

        mapMarker.addListener('click', () => {
          infoWindow.open(map, mapMarker);
        });
      });
    }
  }, [map, markers]);

  if (!isLoaded) {
    return (
      <div
        className={`bg-gray-100 rounded-lg flex items-center justify-center ${className}`}
        style={{ height }}
      >
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00646C] mx-auto mb-2"></div>
          <p className="text-sm text-gray-500">Loading map...</p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={mapRef}
      className={`rounded-lg overflow-hidden ${className}`}
      style={{ height }}
    />
  );
}
