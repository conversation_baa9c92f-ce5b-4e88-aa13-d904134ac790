import { cn } from '@/lib/utils';
import { ReactNode } from 'react';

interface ICardGallery {
  children?: ReactNode;
  gridContainerStyle?: string;
  backgroundContainerStyle?: string;
}

function CardGallery({
  children,
  gridContainerStyle,
  backgroundContainerStyle,
}: ICardGallery) {
  return (
    <div className=" w-full flex flex-col items-center justify-center px-[20px] relative ">
      <div
        className={cn(
          'grid grid-cols-1 mx-auto  z-10 md:grid-cols-2 gap-x-14 md:gap-x-10 lg:gap-x-14  gap-y-14 md:gap-y-4 lg:gap-y-1 text-center md:text-left  font-medium text-black',
          gridContainerStyle,
        )}
      >
        {children}
      </div>
      <div
        className={cn(
          ' absolute top-[-20px] md:top-[30px] left-0 right-0 bottom-[-40px] md:bottom-[20%] bg-[#E6E6E6]',
          backgroundContainerStyle,
        )}
      ></div>
    </div>
  );
}

export default CardGallery;
