import MiddleContainer from '@/components/middle_container';
import PageBanner from '@/components/page_banner';
import { Metadata, ResolvingMetadata } from 'next';
import { getTranslations } from 'next-intl/server';
import ServicesSidebar from './components/ServicesSidebar';
import QuoteCard from './components/QuoteCard';
import ImageGallery from './components/ImageGallery';
import ContentSection from './components/ContentSection';
import { services } from '../data/data';
import { redirect } from 'next/navigation';
import { headers } from 'next/headers';

export async function generateMetadata(
  { params: { locale, id } }: { params: { locale: string; id: string } },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const s = await getTranslations({
    locale,
    namespace: 'Components.servicesList',
  });
  const selectedService = services.find((ser) => ser.slug === id);
  if (!selectedService) {
    redirect('/services');
  }
  return {
    title: s(selectedService?.titleKey),
    description: `${s(selectedService?.titleKey)} | <PERSON> Moving`,
    metadataBase: new URL(
      process.env.APP_BASE_URL ?? `https://${headers().get('host')}`,
    ),
  };
}

export default async function Services({
  params: { id },
}: {
  params: { id: string };
}) {
  const t = await getTranslations('Components.servicesPage');
  const c = await getTranslations('Components.common');
  const s = await getTranslations('Components.servicesList');

  const selectedService = services.find((ser) => ser.slug === id);

  if (!selectedService) {
    redirect('/services');
  }

  return (
    <div className="w-full flex flex-col gap-32  self-center max-w-[1920px]  pb-24  ">
      <PageBanner
        title={s(selectedService?.titleKey)}
        background="linear-gradient(to right, #8e5c7d, #2d4c5c)"
        items={[{ title: t('bannerTitle') }]}
      />
      <MiddleContainer>
        <div className=" grid gap-6  grid-cols-1  lg:grid-cols-[300px_1fr] lg:gap-8">
          <aside className="space-y-6 justify-self-center w-full  max-w-[600px]  ">
            <ServicesSidebar />
            <QuoteCard />
          </aside>
          <main className="space-y-6">
            <ImageGallery />
            <ContentSection title={s(selectedService?.titleKey)} />
          </main>
        </div>
      </MiddleContainer>
    </div>
  );
}
