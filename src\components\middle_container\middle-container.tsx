import { cn } from '@/lib/utils';
import { ReactNode } from 'react';

interface IMiddleContainer {
  children: ReactNode;
  className?: string;
}

function MiddleContainer({ children, className }: IMiddleContainer) {
  return (
    <div
      className={cn(
        'flex flex-col gap-32  items-stretch pb-4 w-full   max-w-screen-2xl  self-center    px-[5%] 2xl:px-0  ',
        className,
      )}
    >
      {children}
    </div>
  );
}

export default MiddleContainer;
