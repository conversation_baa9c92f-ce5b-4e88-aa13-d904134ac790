'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Tabs, TabsList, TabsTrigger } from '../tabs';
import { ImageData } from '@/models/ImageData';
import Image from 'next/image';

interface ItemShowcaseProps<T> {
  items: T[];
  renderedItems: React.ReactNode[];
  className?: string;
}

const SlideInDiv = ({
  items,
  activeIndex,
  hovering,
  renderedItems,
}: {
  items: any[];
  activeIndex: number;
  hovering: boolean;
  renderedItems: React.ReactNode[];
}) => {
  return (
    <div className="relative w-full  h-[600px] overflow-hidden   ">
      <AnimatePresence mode="popLayout">
        {items.map((item, idx) => {
          const isActive = idx === activeIndex;
          const distance = idx - activeIndex;

          return (
            <motion.div
              key={item.name}
              initial={{
                y: distance > 0 ? '100%' : '-100%',
                opacity: 0,
              }}
              animate={{
                y: isActive ? 0 : `${distance * 100}%`,
                opacity: isActive ? 1 : 0.3,
                zIndex: items.length - Math.abs(distance),
              }}
              exit={{
                y: distance > 0 ? '100%' : '-100%',
                opacity: 0,
              }}
              transition={{
                type: 'spring',
                stiffness: 100,
                damping: 20,
                mass: 1.3,
                duration: 0.5,
              }}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
              }}
            >
              {renderedItems[idx]}
            </motion.div>
          );
        })}
      </AnimatePresence>
    </div>
  );
};

export default function ItemShowcase<
  T extends { name: string; icon: ImageData },
>({ items, renderedItems, className }: ItemShowcaseProps<T>) {
  const [activeIndex, setActiveIndex] = useState(0);
  const [hovering, setHovering] = useState(false);

  return (
    <Tabs
      className={cn(
        'flex gap-10 md:gap-0 flex-col md:flex-row-reverse items-start bg-gray-100/50 rounded-2xl  overflow-hidden flex-grow',
        className,
      )}
      defaultValue={items[0]?.name}
      onValueChange={(value) =>
        setActiveIndex(items.findIndex((item) => item.name === value))
      }
    >
      <TabsList
        className="bg-transparent flex-col gap-2 items-start md:self-center h-fit flex w-1/4"
        onMouseEnter={() => setHovering(true)}
        onMouseLeave={() => setHovering(false)}
      >
        {items.map(({ name, icon }, index) => (
          <TabsTrigger
            key={name}
            className="pb-2 pl-0 text-secondary text-xl bg-transparent data-[state=active]:text-secondary data-[state=active]:text-2xl data-[state=active]:font-medium font-normal data-[state=active]:shadow-none"
            value={name}
          >
            <motion.div
              animate={{
                y: activeIndex === index ? [0, 10, 0] : 0,
              }}
              transition={{
                duration: 0.5,
              }}
              className="flex flex-row items-center gap-4"
            >
              {icon && (
                <Image
                  src={icon.path}
                  width={70}
                  height={70}
                  alt="icon"
                  className=""
                />
              )}
              {name}
            </motion.div>
          </TabsTrigger>
        ))}
      </TabsList>
      <div className=" w-full  md:w-3/4   min-h-[600px]  ">
        <SlideInDiv
          items={items}
          activeIndex={activeIndex}
          hovering={hovering}
          renderedItems={renderedItems}
        />
      </div>
    </Tabs>
  );
}
