import { TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { ChevronRight } from 'lucide-react';

interface IFaqTabList {
  categories: string[];
}

function FaqTabList({ categories }: IFaqTabList) {
  return (
    <TabsList className=" flex flex-col h-fit items-start justify-start gap-2 bg-primary px-4   py-8 rounded-lg ">
      {categories.map((category) => (
        <TabsTrigger
          key={category}
          value={category}
          className={cn(
            ' text-base  ml-0  justify-between w-full  md:w-[200px] xl:w-[350px]  py-1 group  text-left text-white  data-[state=active]:text-secondary rounded-lg  ',
          )}
        >
          <span className="text-left   ">{category}</span>
          <ChevronRight className="h-4 w-4" />
        </TabsTrigger>
      ))}
    </TabsList>
  );
}

export default FaqTabList;
