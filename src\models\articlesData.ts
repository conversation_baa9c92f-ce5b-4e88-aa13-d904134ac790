import { BriefData } from './BriefData';
import { ImageData } from './ImageData';

export type Comment = {
  assignTo?: string;
  id: number;
  status: BriefData;
  content: string;
  createdAt: Date;
  createdBy: BriefData;
};

export type Status = {
  id: number;
  statusCode: string;
};

export interface ArticleBrief extends BriefData {
  author?: string;
  summary?: string;
  tags?: string;
  categories: BriefData[];
  languages: string[];
  createdAt: Date;
  updatedAt?: Date;
  scheduledPublishAt?: Date;
  image?: ImageData;
  slug: string;
}

export interface Article extends ArticleBrief {
  content: string;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string;
}
