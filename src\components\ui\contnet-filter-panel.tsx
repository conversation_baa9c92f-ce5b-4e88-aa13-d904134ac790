'use client';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { LayoutGrid, List } from 'lucide-react';

import { FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import Field from './inputs/field';
type FormData = {
  sortBy: string;
};
const ContentFilterPanel = () => {
  const [layout, setLayout] = useState<'list' | 'cards'>('cards');
  const methods = useForm<FormData>({
    mode: 'onChange',
    defaultValues: {
      sortBy: '',
    },
  });

  const onSubmit: SubmitHandler<FormData> = (data) => {
    console.log('Form data:', data);
  };
  console.log('sortBy', methods.watch('sortBy'));

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="w-full">
        <div className="flex justify-between items-center p-4 bg-background border  w-full ">
          <div className="space-x-2">
            <Button
              variant={layout === 'cards' ? 'default' : 'outline'}
              size="icon"
              onClick={() => setLayout('cards')}
              type="button"
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
            <Button
              variant={layout === 'list' ? 'default' : 'outline'}
              size="icon"
              onClick={() => setLayout('list')}
              type="button"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
          <Field
            type={{
              type: 'select',
              props: {
                options: [
                  { label: 'Newest', value: 'newest' },
                  { label: 'Oldest', value: 'oldest' },
                ],
                placeholder: 'Sort By',
              },
            }}
            name={'sortBy'}
            label={'Sort By'}
            control={methods.control}
          />
        </div>
      </form>
    </FormProvider>
  );
};

export default ContentFilterPanel;
