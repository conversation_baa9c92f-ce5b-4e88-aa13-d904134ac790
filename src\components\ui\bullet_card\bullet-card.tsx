import { motion } from 'framer-motion';
import CardGenerator from '../card_generator';

export interface IBulletCard {
  title: string;
  list: string[];
}

const underlineVariants = {
  hidden: { width: '0%' },
  visible: {
    width: '100%',
    transition: {
      duration: 1,
      delay: 0.3,
      ease: 'linear',
    },
  },
};

function BulletCard({ title, list }: IBulletCard) {
  return (
    <CardGenerator
      className="h-full shadow-none border-none transition-all duration-500 py-0 px-0 group gap-12 max-w-3xl "
      headerClassName="py-0 px-0"
      title={
        <div className="relative pb-5">
          <h3 className="text-2xl font-medium text-primary max-w-[250px]">
            {title}
          </h3>
          <motion.div
            className="absolute bottom-0 left-0 h-[2px] bg-primary max-w-[250px] "
            initial="hidden"
            whileInView="visible"
            variants={underlineVariants}
            viewport={{ once: true, amount: 0.2 }}
          />
        </div>
      }
    >
      <ul className="w-full flex flex-col gap-5 text-black font-devanagari font-normal text-lg leading-[30px]">
        {list.map((item, index) => (
          <li
            className="w-full flex flex-row items-baseline gap-1 overflow-visible"
            key={index}
          >
            <div className="min-w-2 w-2 h-2 bg-primary"></div>
            <p>{item}</p>
          </li>
        ))}
      </ul>
    </CardGenerator>
  );
}

export default BulletCard;
