'use client';

import { useEffect, useRef, ReactNode } from 'react';
import { useScroll, MotionValue, useTransform, motion } from 'framer-motion';
import Lenis from 'lenis';
import { cn } from '@/lib/utils';

interface ParallaxCardProps<T> {
  targetScale: number;
  progress: MotionValue<number>;
  range: [number, number];
  index: number;
  data: T;
  mainClassName?: string;
  renderContent: (data: T, index: number) => ReactNode;
}

function ParallaxCard<T>({
  targetScale,
  progress,
  range,
  index,
  data,
  renderContent,
  mainClassName,
}: ParallaxCardProps<T>) {
  const containerRef = useRef<HTMLDivElement>(null);

  const scale = useTransform(progress, range, [1, targetScale]);
  const opacityRange = [
    range[0],
    range[0] + (range[1] - range[0]) * 0.5,
    range[1],
  ];
  const opacity = useTransform(progress, opacityRange, [1, 1, 0]);
  const translateY = useTransform(progress, range, [0, -50]);

  return (
    <div
      ref={containerRef}
      className="w-full sticky top-0 h-screen flex items-center justify-center"
    >
      <motion.div
        className={cn(
          'w-full relative md:h-[650px] flex flex-col gap-10 md:gap-0 md:flex-row overflow-hidden rounded-xl shadow-xl',
          mainClassName,
        )}
        style={{ scale, opacity, y: translateY }}
      >
        {renderContent(data, index)}
      </motion.div>
    </div>
  );
}

interface CardParallaxPanelProps<T> {
  heading: ReactNode;
  cards: T[];
  renderCard: (card: T, index: number) => ReactNode;
  className?: string;
  cardWrapperClassName?: string;
}

function CardParallaxPanel<T>({
  heading,
  cards,
  renderCard,
  cardWrapperClassName,
}: CardParallaxPanelProps<T>) {
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start start', 'end end'],
  });

  useEffect(() => {
    const lenis = new Lenis();

    function raf(time: number) {
      lenis.raf(time);
      requestAnimationFrame(raf);
    }

    requestAnimationFrame(raf);
  }, []);

  const totalCards = cards.length;
  const progressSegment = 1 / (totalCards - 1);

  return (
    <section
      className={`w-full flex flex-col items-center justify-center gap-6 `}
    >
      {heading}
      <div className="w-full" ref={containerRef}>
        {cards.map((card, index) => (
          <ParallaxCard<T>
            key={index}
            targetScale={0.7}
            progress={scrollYProgress}
            range={[index * progressSegment, (index + 1) * progressSegment]}
            index={index}
            data={card}
            renderContent={renderCard}
            mainClassName={cardWrapperClassName}
          />
        ))}
      </div>
    </section>
  );
}

export default CardParallaxPanel;
