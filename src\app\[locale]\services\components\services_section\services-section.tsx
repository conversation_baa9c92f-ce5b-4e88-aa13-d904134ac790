import { getTranslations } from 'next-intl/server';
import { services } from '../../data/data';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import Image from 'next/image';

interface IServicesSection {}

async function ServicesSection({}: IServicesSection) {
  const t = await getTranslations('Components.servicesPage');
  const c = await getTranslations('Components.common');
  const s = await getTranslations('Components.servicesList');

  return (
    <section className="w-fit  self-center  flex py-12 md:py-16 lg:py-20">
      <div className=" px-4 md:px-6 flex items-center justify-center  ">
        <div className="grid grid-cols-1 gap-x-6 gap-y-16  md:grid-cols-2 xl:grid-cols-3  ">
          {services.map((service, index) => (
            <Link
              key={index}
              href={`/services/${service.slug}`}
              className="group"
            >
              <Card className="overflow-hidden border-0 bg-background shadow-none transition-colors hover:bg-muted/50">
                <CardContent className="p-0">
                  <div className="relative h-[450px] w-[400px] overflow-hidden rounded-lg rounded-b-none ">
                    <Image
                      src={service.images[0].path}
                      alt={service.images[0].alt ?? s(service.titleKey)}
                      className="object-cover"
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                  </div>
                  <div className="p-4 text-center bg-muted/50">
                    <h3 className="text-sm font-medium tracking-wide text-muted-foreground">
                      {s(service.titleKey)}
                    </h3>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}

export default ServicesSection;
