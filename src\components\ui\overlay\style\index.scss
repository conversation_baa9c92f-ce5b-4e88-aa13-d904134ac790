.overlay-container {
  display: flex;
  position: fixed;
  align-content: stretch;
  width: -webkit-fill-available;
  width: 100%;
  height: 100%;
  height: -webkit-fill-available;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  z-index: 11;
  padding: 2vh 1vw;

  > div {
    display: flex;
    position: absolute;
    align-content: stretch;
    width: 100%;
    width: -webkit-fill-available;
    height: 100%;
    height: -webkit-fill-available;
    justify-content: center;
    align-items: center;
    pointer-events: none;
    > .backdrop {
      display: flex;
      position: fixed;
      width: 100vw;
      height: 100vh;
      flex-direction: column;
      align-content: center;
      justify-content: center;
      align-items: center;
    }
    > .layer {
      display: flex;
      flex-direction: column;
      z-index: 1;
      > .close-btn {
        align-self: flex-end;
        border-radius: 5px;
        padding-bottom: 5px;
        //fused
        &.inner {
          position: absolute;
          margin: 15px;
        }
        &.outer {
          position: static;
          margin-right: -25px;
        }
      }
      &:focus {
        outline: none;
      }
      @keyframes appear-top {
        0% {
          transform: translate(0, -50vh);
        }
        100% {
          transform: translate(0, 0);
        }
      }
      @keyframes appear-left {
        0% {
          transform: translate(-50vw, 0);
        }
        100% {
          transform: translate(0, 0);
        }
      }
      @keyframes appear-right {
        0% {
          transform: translate(50vw, 0);
        }
        100% {
          transform: translate(0, 0);
        }
      }
      @keyframes zoom {
        0% {
          transform: scale(0);
        }
        100% {
          transform: translate(1);
        }
      }
      @keyframes appear-bottom {
        0% {
          transform: translate(0, 50vh);
        }
        100% {
          transform: translate(0, 0);
        }
      }
    }
  }
}
