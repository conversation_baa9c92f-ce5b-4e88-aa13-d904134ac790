import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';

const servicesLinks: {
  nameKey: string;
  href: string;
}[] = [
  {
    nameKey: 'service1Title',
    href: '/services',
  },
  {
    nameKey: 'service2Title',
    href: '/services',
  },
  {
    nameKey: 'service3Title',
    href: '/services',
  },
  {
    nameKey: 'service4Title',
    href: '/services',
  },
  {
    nameKey: 'service5Title',
    href: '/services',
  },
  {
    nameKey: 'service6Title',
    href: '/services',
  },
];

interface IServicesDropdown {}

function ServicesDropdown({}: IServicesDropdown) {
  const t = useTranslations('Components.navigation');
  return (
    <div className="w-full lg:min-w-[900px]  bg-primary lg:px-4  xl:px-8 pb-8  pt-12 z-50  ">
      <div className="w-full flex flex-col-reverse items-start lg:flex-row lg:gap-0  xl:gap-10   ">
        <div className="w-full px-2 lg:w-1/3  pt-5">
          <h2 className="text-3xl font-bold text-white mb-6">Services</h2>
          <div className="flex flex-col gap-4">
            {servicesLinks.map((link, index) => (
              <Link
                key={index}
                href={link.href}
                className="text-white hover:text-secondary text-lg  duration-300 transition-colors"
              >
                {t(link.nameKey)}
              </Link>
            ))}
          </div>
        </div>
        <div className="w-full hidden lg:flex flex-col gap-3 lg:w-2/3 mb-8 lg:mb-0  overflow-hidden   ">
          <p className="text-white text-lg ">
            Lorem ipsum lorem ipsum lorem ipsum lorem ipsum lorem ipsum
          </p>
          <div className="w-full hidden lg:flex  mb-8 lg:mb-0 aspect-[4/3]  rounded-xl overflow-hidden relative  ">
            <Image
              src="/homepage/dropdown-image.jpeg"
              alt="Services dropdown image"
              fill
              className="object-contain  h-full w-full "
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default ServicesDropdown;
