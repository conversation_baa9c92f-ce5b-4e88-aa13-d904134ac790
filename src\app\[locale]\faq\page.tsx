import { Metadata, ResolvingMetadata } from 'next';
import { getTranslations } from 'next-intl/server';
import FaqSection from './components/faq_section';
import { getQueryClient } from '@/utils/query-client';
import faqQuery from '@/services/queries/faqQuery';
import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import PageBanner from '@/components/page_banner';
import { headers } from 'next/headers';

export async function generateMetadata(
  { params: { locale } }: { params: { locale: string } },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const t = await getTranslations({
    locale,
    namespace: 'Components.FAQPage',
  });
  return {
    title: t('metaDataTitle'),
    description: t('metaDataDesc'),
    metadataBase: new URL(
      process.env.APP_BASE_URL ?? `https://${headers().get('host')}`,
    ),
  };
}

export default async function Faq() {
  const t = await getTranslations('Components.FAQPage');
  const c = await getTranslations('Components.common');

  const queryClient = getQueryClient();
  await queryClient.prefetchQuery({
    queryKey: [...faqQuery.tags],
    queryFn: faqQuery.get,
  });

  return (
    <div className="w-full flex flex-col gap-32  overflow-x-hidden  self-center max-w-[1920px]  pb-24  ">
      <PageBanner
        title={t('bannerTitle')}
        background="linear-gradient(to right, #8e5c7d, #2d4c5c)"
        items={[{ title: t('bannerSubTitle') }]}
      />
      <HydrationBoundary state={dehydrate(queryClient)}>
        <FaqSection />
      </HydrationBoundary>
    </div>
  );
}
