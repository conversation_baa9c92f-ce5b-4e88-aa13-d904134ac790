'use client';

import React, { Children, ReactNode, useState } from 'react';
import Autoplay from 'embla-carousel-autoplay';
import { cn } from '@/lib/utils';
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from '../carousel';
import Fade from 'embla-carousel-fade';

interface IDottedCarousel {
  children: ReactNode;
  className?: string;
  dotted?: boolean;
  full?: boolean;
}

function DottedCarousel({
  children,
  className,
  dotted = true,
  full = true,
}: IDottedCarousel) {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);

  React.useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on('select', () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  return (
    <div className="flex">
      {dotted && (
        <div className="flex flex-col items-center justify-start gap-3 py-1 pl-4  pr-8 ">
          {[...Array(count)].map((_, index) => (
            <div
              key={index}
              className={cn(
                'h-5 w-5 rounded-full bg-gray-400 transition-all duration-100 hover:bg-primary cursor-pointer',
                index === current - 1 && 'bg-primary',
              )}
              onClick={() => api?.scrollTo(index)}
            />
          ))}
        </div>
      )}
      <Carousel
        className={cn('h-fit flex-grow', className)}
        setApi={setApi}
        plugins={[
          Autoplay({
            delay: 8000,
            ...(full ? [Fade()] : []),
          }),
        ]}
        opts={{
          loop: true,
          axis: 'y',
        }}
      >
        <CarouselContent>
          {Children.toArray(children)?.map((item, index) => (
            <CarouselItem key={index}>{item}</CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>
    </div>
  );
}

export default DottedCarousel;
