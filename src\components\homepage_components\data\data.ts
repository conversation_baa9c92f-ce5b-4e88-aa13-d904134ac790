import { IconComponent } from '@/models/IconType';
import { ImageData } from '@/models/ImageData';
import { BsCoin } from 'react-icons/bs';
import { GrMapLocation } from 'react-icons/gr';
import { ImTruck } from 'react-icons/im';

export const bannersList: {
  image: ImageData;
  titleKey: string;
  // actionTextKey: string;
  href: string;
}[] = [
  {
    image: {
      path: '/homepage/homepage-banner.png',
      alt: 'banner-image-1',
      name: 'banner-image-1',
    },
    titleKey: 'banner1TitleKey',
    href: '/',
  },
  {
    image: {
      path: '/homepage/services/service-2-image.jpeg',
      alt: 'banner-image-2',
      name: 'banner-image-2',
    },
    titleKey: 'banner2TitleKey',
    href: '/',
  },
];

export const workProcessList: {
  titleKey: string;
  descriptionP1key: string;
  descriptionP2Key: string;
  icon: IconComponent;
}[] = [
  {
    titleKey: 'workProcess1Title',
    descriptionP1key: 'workProcess1DescP1',
    descriptionP2Key: 'workProcess1DescP2',
    icon: GrMapLocation,
  },
  {
    titleKey: 'workProcess2Title',
    descriptionP1key: 'workProcess2DescP1',
    descriptionP2Key: 'workProcess2DescP2',
    icon: ImTruck,
  },
  {
    titleKey: 'workProcess3Title',
    descriptionP1key: 'workProcess3DescP1',
    descriptionP2Key: 'workProcess3DescP2',
    icon: BsCoin,
  },
];

export const testimonialList: {
  textKey: string;
  nameKey: string;
}[] = [
  {
    textKey: 'testimonial1Text',
    nameKey: 'testimonial1Name',
  },
  {
    textKey: 'testimonial2Text',
    nameKey: 'testimonial2Name',
  },
  {
    textKey: 'testimonial3Text',
    nameKey: 'testimonial3Name',
  },
];
