import { SitemapRow } from './type';
import { pathnames } from '@/i18n.config';

export default async function generateSitemap(): Promise<SitemapRow[]> {
  return [
    ...Object.entries(pathnames)
      .filter((path) => !path[0].includes('['))
      .map(
        ([path, locale]: [
          string,
          string | { en: string; fr: string },
        ]): SitemapRow => {
          return {
            url: path,
            alternateRefs:
              typeof locale === 'object'
                ? [
                    {
                      href: `/en${locale.en}`,
                      hreflang: 'en',
                    },
                    {
                      href: `/fr${locale.fr}`,
                      hreflang: 'fr',
                    },
                  ]
                : [],
            changeFrequency: 'monthly',
          };
        },
      ),
  ];
}
