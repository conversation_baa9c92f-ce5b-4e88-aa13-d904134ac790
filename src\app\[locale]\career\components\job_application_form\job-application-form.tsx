'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useTranslations } from 'next-intl';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

import { Check, X } from 'lucide-react';
import { ReloadIcon } from '@radix-ui/react-icons';
import { cn } from '@/lib/utils';

import FileUploadPreview from '@/components/ui/file_upload_preview';
import { useMutation } from '@tanstack/react-query';
import RequestQuery from '@/services/queries/contact';
import { verifyRecaptcha } from '@/services/queries/email';
import { Textarea } from '@/components/ui/inputs/textarea';

export const JobApplicationFormSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
  phoneNumber: z.string().min(1, 'Phone number is required'),
  message: z.string().optional(),
  CV: z.instanceof(File),
  otherDoc: z.instanceof(File).optional(),
});

export default function JobApplicationForm() {
  const c = useTranslations('Components.common');
  const t = useTranslations('Components.Contact');
  const [loading, setLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | undefined>(undefined);
  const { executeRecaptcha } = useGoogleReCaptcha();

  const {
    isSuccess: isEmailSuccess,
    isError: isEmailError,
    isPending: isEmailPending,
    mutateAsync: SendCareerRequest,
    error: emailError,
  } = useMutation({
    mutationKey: RequestQuery.tags,
    mutationFn: RequestQuery.SendCareerRequest,
  });

  const form = useForm<z.infer<typeof JobApplicationFormSchema>>({
    resolver: zodResolver(JobApplicationFormSchema),
    defaultValues: {
      lastName: '',
      firstName: '',
      email: '',
      phoneNumber: '',
      message: '',
      CV: undefined,
      otherDoc: undefined,
    },
  });
  const { resetField } = form;

  async function onSubmit(data: z.infer<typeof JobApplicationFormSchema>) {
    if (!executeRecaptcha) {
      setError('Recaptcha not yet available.');
      return;
    }

    setLoading(true);

    try {
      const token = await executeRecaptcha('submit_contact_form');

      if (!token) {
        setError('Captcha verification failed. Please try again.');
        return;
      }

      const isRecaptchaValid = await verifyRecaptcha(token);

      if (isRecaptchaValid) {
        const response = await SendCareerRequest(data);

        if (!response) {
          setError('There was an error submitting the form. Please try again.');
        }
        setIsSuccess(true);
      } else {
        setError('Recaptcha verification failed. Please try again.');
      }

      form.reset();
    } catch (err) {
      console.error(' form submission error:', err);
      setError('There was an error submitting the form. Please try again.');
    } finally {
      setLoading(false);
    }
  }
  return (
    <div className="w-full p-4 lg:p-8  duration-500 transition-all flex flex-col gap-16 max-w-[800px] self-center  ">
      <h2 className="text-2xl md:text-3xl font-medium text-foreground w-full text-left  max-w-[800px] ">
        {t('jobSubTitle')}
      </h2>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-normal">
                    {t('firstName')} *
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder={t('firstNamePlaceholder')}
                      className="rounded-xl border-primary "
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-normal">
                    {t('lastName')} *
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder={t('lastNamePlaceholder')}
                      className="rounded-xl border-primary "
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-normal">{t('email')} *</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      placeholder={t('emailPlaceholder')}
                      className="rounded-xl border-primary "
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-normal">
                    {t('phoneNumber')} *
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="tel"
                      placeholder={t('phonePlaceholder')}
                      className="rounded-xl border-primary "
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex flex-col gap-4">
            <div>
              <h3 className="text-xl font-medium pb-2 text-black">Documents</h3>
              <hr className="" />
            </div>
            <div className="w-full  flex flex-row gap-10 items-start flex-wrap justify-start ">
              <FormField
                control={form.control}
                name="CV"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t('UploadCVLabel')}
                      <span className="text-red-500"> *</span>
                    </FormLabel>
                    <FormControl>
                      <div className="flex flex-col items-center justify-center bg-secondary rounded-lg w-fit px-6 py-2 ">
                        <input
                          type="file"
                          id="CVFile"
                          className="z-[-1] w-[0.1px] h-[0.1px] opacity-0 overflow-hidden absolute"
                          // disabled={isPending}
                          onChange={(e) => {
                            field.onChange(
                              e.target.files ? e.target.files[0] : null,
                            );
                          }}
                        />
                        <label
                          className="text-base w-full text-white cursor-pointer"
                          htmlFor="CVFile"
                        >
                          {t('UploadCVPlaceholder')}
                        </label>
                      </div>
                    </FormControl>
                    {field.value && field.value.size > 0 && (
                      <FileUploadPreview
                        file={field.value}
                        onCancel={() => {
                          resetField('CV');
                        }}
                      />
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="otherDoc"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('UploadOtherLabel')}</FormLabel>
                    <FormControl>
                      <div className="flex flex-col items-center justify-center bg-secondary rounded-lg w-fit px-6 py-2 ">
                        <input
                          type="file"
                          id="OtherFile"
                          className="z-[-1] w-[0.1px] h-[0.1px] opacity-0 overflow-hidden absolute"
                          // disabled={isPending}
                          onChange={(e) => {
                            field.onChange(
                              e.target.files ? e.target.files[0] : null,
                            );
                          }}
                        />
                        <label
                          htmlFor="OtherFile"
                          className="text-base w-full text-white cursor-pointer"
                        >
                          {t('UploadOtherPlaceholder')}
                        </label>
                      </div>
                    </FormControl>
                    {field.value && field.value.size > 0 && (
                      <FileUploadPreview
                        file={field.value}
                        onCancel={() => {
                          resetField('otherDoc');
                        }}
                      />
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <FormField
            control={form.control}
            name="message"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-normal">{t('message')}</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder={t('messagePlaceholder')}
                    className="h-32  rounded-xl border-primary "
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="flex justify-center">
            <Button
              type="submit"
              className={cn(
                'w-full max-w-xs',
                error
                  ? 'bg-red-600 hover:bg-red-700'
                  : 'bg-secondary hover:bg-primary/90',
              )}
              disabled={loading}
            >
              {loading || isEmailPending ? (
                <ReloadIcon className="mr-2 h-6 w-6 animate-spin" />
              ) : isSuccess && isEmailSuccess ? (
                <>
                  <Check className="mr-2 h-6 w-6" />
                  {c('success')}
                </>
              ) : error || isEmailError ? (
                <>
                  <X className="mr-2 h-6 w-6" />
                  {error ? c('error') : emailError && emailError?.message}
                </>
              ) : (
                t('submit')
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
