'use client';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { ImageData } from '@/models/ImageData';
import Image from 'next/image';

export interface IExtendedProductItem {
  name?: string;
  code?: string;
  description: string;
  image: ImageData;
}

function ExtendedProductItem({
  name,
  code,
  description,
  image,
}: IExtendedProductItem) {
  return (
    <div className="flex flex-col gap-4 items-center w-full h-full ">
      <Tooltip>
        <TooltipTrigger>
          <div className="flex-grow flex items-center justify-center min-h-[150px] w-full">
            <Image
              src={image.path}
              alt={image.alt ?? `${name} image`}
              width={200}
              height={200}
              className="object-contain max-w-[200px] max-h-[150px]"
            />
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <div className="p-4">
            <Image
              src={image.path}
              alt={image.alt ?? `${name} image`}
              width={400}
              height={400}
              className="object-contain"
            />
          </div>
        </TooltipContent>
      </Tooltip>
      <div className="w-full flex flex-col gap-2 ">
        {code && (
          <p className="text-base text-secondary font-normal mt-4 text-center">
            {code}
          </p>
        )}
        {(name || description) && (
          <div className="w-full flex flex-col  gap-2 ">
            {name && (
              <p className="text-2xl font-bold mt-4 text-center leading-8 ">
                {name}
              </p>
            )}
            <p className="text-base font-normal text-center leading-8 ">
              {description}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

export default ExtendedProductItem;
