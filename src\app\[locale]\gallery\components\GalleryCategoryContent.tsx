import { GallerySubcategory, GalleryImage } from '@/models/GalleryModels';
import GallerySubcategorySection from './GallerySubcategorySection';

interface GalleryCategoryContentProps {
  subcategories: GallerySubcategory[];
  imagesBySubcategory: Record<number, GalleryImage[]>;
}

export default function GalleryCategoryContent({
  subcategories,
  imagesBySubcategory,
}: GalleryCategoryContentProps) {
  return (
    <div className="flex flex-col gap-12">
      {subcategories.map((subcat) => (
        <GallerySubcategorySection
          key={subcat.id}
          subcategory={subcat}
          images={imagesBySubcategory[subcat.id] || []}
        />
      ))}
    </div>
  );
}
