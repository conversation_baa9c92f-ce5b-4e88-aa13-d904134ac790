'use client';
import { ReactNode } from 'react';
import './style/index.scss';
import { createPopper } from '@popperjs/core';
import { OverlayOptions, PopperTargetType } from './types';
import { modal } from './stores';
import { XIcon } from 'lucide-react';
import { Button } from '../button';

type OverlayItemProps = OverlayOptions & {
  children?: ReactNode;
};

export function OverlayItem({
  children,
  isDimmed = false,
  clickThrough = false,
  backdropColor,
  width,
  height,
  closeBtn,
  popperTarget,
  closeOnClickOutside,
  closeOnBlur,
  autoFocus = true,
  position,
  transition = 'zoom',
  onClose,
  defaultCloseFallback = true,
  style,
  closeMethod,
  clickable = true,
  closable = true,
  type = 'modal',
}: OverlayItemProps) {
  const closeOverlay = () => {
    onClose?.();
    if (!closable) return;
    if (closeMethod) closeMethod();
    else if (defaultCloseFallback)
      if (type == 'tooltip') {
        // tooltip.close();
      } else modal.close();
  };
  return (
    <>
      {backdropColor !== false && (
        <div
          className="backdrop bg-black/80"
          style={{
            backgroundColor: isDimmed ? backdropColor : undefined,
            pointerEvents: clickThrough ? 'none' : 'all',
          }}
          onClick={
            closable && closeOnClickOutside
              ? (e) => {
                  closeOverlay();
                  e.stopPropagation();
                }
              : undefined
          }
        ></div>
      )}
      <div
        className="layer"
        style={{
          ...style,
          width: width,
          height: height,
          position: position && 'absolute',
          top: position?.top,
          bottom: position?.bottom,
          left: position?.left,
          right: position?.right,
          animation:
            transition != 'none'
              ? `${transition} .1s forwards ease-out`
              : undefined,
          pointerEvents: clickable ? 'all' : 'none',
        }}
        {...(autoFocus ? { tabIndex: -1 } : {})}
        onBlur={
          closable && closeOnBlur
            ? (event) => {
                if (!event.currentTarget.contains(event.relatedTarget)) {
                  closeOverlay();
                }
              }
            : undefined
        }
        ref={(e) => {
          e?.focus();
          if (e != null) {
            if (popperTarget)
              createPopper(
                (popperTarget as PopperTargetType).target ?? popperTarget,
                e,
                {
                  modifiers: [
                    {
                      name: 'preventOverflow',
                      options: {
                        padding: 30,
                        altAxis: true,
                      },
                    },
                    {
                      name: 'offset',
                      options: {
                        offset: [0, 5],
                      },
                    },
                    {
                      name: 'flip',
                      options: {
                        altBoundary: true,
                        fallbackPlacements: ['bottom'],
                      },
                    },
                  ],
                  ...((popperTarget as PopperTargetType).options ?? {
                    placement: 'auto-end',
                  }),
                },
              );
          }
        }}
      >
        {closable && closeBtn && (
          <div
            className={`close-btn ${
              typeof closeBtn == 'string' ? closeBtn : closeBtn.placement
            }`}
          >
            {typeof closeBtn == 'string' ? (
              <Button
                variant="outline"
                size="icon"
                onClick={() => closeOverlay()}
              >
                <XIcon className="h-4 w-4" />
              </Button>
            ) : (
              closeBtn.placement
            )}
          </div>
        )}
        {children}
      </div>
    </>
  );
}
