'use client';

import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3';

export default function GoogleReCaptcha({ children }: any) {
  return (
    <>
      <GoogleReCaptchaProvider
        reCaptchaKey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY!}
        scriptProps={{
          async: false, // This is important for Next.js
          defer: false, // This is important for Next.js
          appendTo: 'head', // This is important for Next.js
        }}
      >
        {children}
      </GoogleReCaptchaProvider>
    </>
  );
}
