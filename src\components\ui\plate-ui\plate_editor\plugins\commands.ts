import {
  getPluginType,
  insertNodes,
  PlateEditor,
  Value,
} from '@udecode/plate-common';
import { TImageElement } from '../../image-element';
import { ELEMENT_IMAGE } from '@udecode/plate-media';

export const insertImage = (
  editor: PlateEditor<Value>,
  props: {
    url: string;
    alt: string;
  },
) => {
  insertNodes<TImageElement>(
    editor,
    {
      ...props,
      type: getPluginType(editor, ELEMENT_IMAGE),
      children: [{ text: '' }],
    },
    {
      nextBlock: true,
    },
  );
};
