import { TooltipProvider } from '../ui/tooltip';
import IntlProvider from './providers/intl-provider';
import QueryProvider from './providers/query-provider';
import ThemeProvider from './providers/theme-provider';
import { ReactNode } from 'react';
import GoogleReCaptcha from './providers/google-captcha-provider';
interface ProviderProps {
  children: ReactNode;
}
export function Provider({ children }: ProviderProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="white"
      enableSystem
      disableTransitionOnChange
    >
      <GoogleReCaptcha>
        <TooltipProvider>
          <IntlProvider>
            <QueryProvider> {children}</QueryProvider>
          </IntlProvider>
        </TooltipProvider>
      </GoogleReCaptcha>
    </ThemeProvider>
  );
}
