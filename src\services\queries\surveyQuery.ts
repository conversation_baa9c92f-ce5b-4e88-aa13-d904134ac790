import { BriefData } from '@/models/BriefData';
import fetcher from './fetcher';
import { AppointmentTimeBrief } from '@/models/RequestData';

const SurveyQuery = {
  tags: ['Survey'] as const,

  getSurveyTypes: async () => fetcher<BriefData[]>(`requests/SurveyTypes`),
  getPropertyTypes: async () => fetcher<BriefData[]>(`requests/PropertyTypes`),
  getAppointmentTimes: async () =>
    fetcher<AppointmentTimeBrief[]>(`requests/AppointmentTimes`),
};
export default SurveyQuery;
