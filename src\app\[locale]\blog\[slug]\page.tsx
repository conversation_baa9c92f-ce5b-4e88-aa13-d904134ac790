import { getTranslations } from 'next-intl/server';
import MiddleContainer from '@/components/middle_container';
import ArticleContent from './components/article_content';
import ArticleSideBar from './components/article_side_bar';
import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getQueryClient } from '@/utils/query-client';
import articlesQuery from '@/services/queries/ArticleQuery';
import { Metadata, ResolvingMetadata } from 'next';
import { headers } from 'next/headers';

export async function generateMetadata(
  {
    params,
  }: {
    params: {
      slug: string;
    };
  },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  // fetch data
  const article = await getQueryClient().fetchQuery({
    queryKey: [...articlesQuery.tags, { slug: params.slug }],
    queryFn: () => articlesQuery.get(params.slug),
  });

  // optionally access and extend (rather than replace) parent metadata
  const previousImages = (await parent).openGraph?.images || [];
  //TODO check image preview
  return {
    title: `${article.seoTitle ?? article.name} | John Gray Moving`,
    description: article.seoDescription ?? article.slug,
    metadataBase: new URL(
      process.env.APP_BASE_URL ?? `https://${headers().get('host')}`,
    ),
    openGraph: {
      images: article.image
        ? ['/images/' + article.image.path, ...previousImages]
        : previousImages,
    },
  };
}

export default async function BlogDetail({
  params,
  searchParams,
}: {
  params: { slug: string };
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  const c = await getTranslations('Components.common');
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery({
    queryKey: [
      ...articlesQuery.tags,
      {
        slug: params.slug,
      },
    ],
    queryFn: () => articlesQuery.get(params.slug),
  });
  await queryClient.prefetchQuery({
    queryKey: [...articlesQuery.tags],
    queryFn: articlesQuery.getBrief,
  });

  return (
    <div className="flex w-full flex-col gap-32 py-24">
      <MiddleContainer>
        <HydrationBoundary state={dehydrate(queryClient)}>
          <section className="w-full flex flex-col-reverse xl:flex-row items-start gap-10 ">
            <ArticleContent slug={params.slug} />
            <ArticleSideBar />
          </section>
        </HydrationBoundary>
      </MiddleContainer>
    </div>
  );
}
