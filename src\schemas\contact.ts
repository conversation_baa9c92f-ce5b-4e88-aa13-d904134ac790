import * as z from 'zod';

const contactSchema = z.object({
  name: z
    .string()
    .max(50, { message: 'Name must be at most 50 characters long' }),
  company: z
    .string()
    .min(7, { message: 'Company name must be at least 7 characters long' })
    .max(50, { message: 'Company name must be at most 50 characters long' }),
  phoneNumber: z
    .string()
    .min(7, { message: 'Phone number must be at least 7 characters long' })
    .max(15, { message: 'Phone number must be at most 15 characters long' }),
  email: z.string().email({ message: 'Invalid email format' }),
  message: z.string(),
});
export type ContactData = z.infer<typeof contactSchema>;
export default contactSchema;
