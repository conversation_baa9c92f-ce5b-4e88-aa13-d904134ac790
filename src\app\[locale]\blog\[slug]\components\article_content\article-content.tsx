'use client';
import AppImage from '@/components/ui/app_image';
import { Badge } from '@/components/ui/Badge';
import { formatDate } from 'date-fns';
import { Calendar } from 'lucide-react';
// import './style/index.scss';
import SocialMediaLinks from '../social_media_links';
import articlesQuery from '@/services/queries/ArticleQuery';
import { useQuery } from '@tanstack/react-query';
import Suspense from '@/components/ui/Suspense';
import { FIT_MODAL, modal } from '@/components/ui/overlay';
import ArticleCard from '../../../components/article_card';
import { useLocale } from 'next-intl';
import { useEffect } from 'react';
import RichTextViewer from '../rich_text_viewer';

interface IArticleContent {
  slug: string;
}

function ArticleContent({ slug }: IArticleContent) {
  const local = useLocale();

  const { data, isLoading, refetch } = useQuery({
    queryKey: [
      ...articlesQuery.tags,
      {
        slug,
      },
    ],
    queryFn: () => articlesQuery.get(slug),
  });

  useEffect(() => {
    refetch();
  }, [local, refetch]);

  const { data: articles, isLoading: isLoadingArticles } = useQuery({
    queryKey: [...articlesQuery.tags],
    queryFn: articlesQuery.getBrief,
  });

  const getRelatedArticles = () => {
    if (!data || !articles) return [];

    const relatedArticles = articles.filter(
      (article) =>
        article.categories.some((category) =>
          data.categories.some(
            (dataCategory) => dataCategory.id === category.id,
          ),
        ) && article.id !== data.id,
    );

    return relatedArticles.slice(0, 4); // Limit to 4 articles
  };

  const relatedArticles = getRelatedArticles();

  return (
    <Suspense isLoading={isLoading || isLoadingArticles}>
      {data && (
        <div className="w-full flex flex-col gap-10">
          <div className="w-full flex flex-col items-center gap-10 shadow-lg pb-10">
            <div
              onClick={() => {
                modal(
                  <AppImage
                    className=" rounded-sm object-cover max-h-[80vh]  "
                    image={data.image}
                    legacy={true}
                  />,
                  FIT_MODAL,
                ).open();
              }}
              className="w-full min-h-[380px] max-h-[380px] relative"
            >
              <AppImage
                image={data.image}
                className="w-full h-full object-cover"
                fill
              />
            </div>
            <div className="w-full flex flex-row gap-2 flex-wrap px-5">
              {data.categories.map((category) => (
                <Badge
                  key={category.id}
                  variant="primary"
                  className="py-2 px-5 text-white rounded-full text-base"
                >
                  {category.name}
                </Badge>
              ))}
            </div>
            <div className="w-full flex flex-col gap-5 px-5">
              <h1 className="text-3xl font-semibold text-black">{data.name}</h1>
              {data.summary && (
                <p className="text-black font-devanagari text-lg font-medium">
                  {data.summary}
                </p>
              )}
              <Badge variant="transparent" className="gap-3 pl-0 items-center">
                <Calendar className="w-5 h-5 text-gray-700" />
                <div className="w-full  flex flex-row items-center justify-between ">
                  <p className="text-gray-700 font-devanagari text-base font-normal">
                    {formatDate(data.createdAt, 'dd/MM/yyyy')}
                  </p>
                  {data.author && (
                    <p className="text-gray-700 font-devanagari text-base font-normal">
                      {data.author}
                    </p>
                  )}
                </div>
              </Badge>
            </div>

            <RichTextViewer
              html={data.content}
              className="ProseMirror w-full px-5"
            />
            <SocialMediaLinks />
          </div>
          {relatedArticles && relatedArticles.length > 0 && (
            <div className="w-full flex flex-col items-start gap-10 shadow-lg px-10 py-16">
              <h3 className="text-3xl font-semibold text-primary">
                Related Posts
              </h3>
              <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-8 ">
                {relatedArticles.map((article, index) => (
                  <ArticleCard
                    index={index}
                    key={article.id}
                    id={article.id}
                    articleNumber={article.id}
                    title={article.name}
                    summary={article.summary}
                    updatedAt={article.updatedAt}
                    createdAt={article.createdAt}
                    image={article.image}
                    slug={article.slug}
                    tags={article.tags}
                  />
                ))}
              </div>
            </div>
          )}
          {/* <div className="w-full flex flex-col gap-16 shadow-lg px-10 py-10">
            <h3 className="text-3xl font-semibold text-primary">
              Comments (preview)
            </h3>
            <div className="w-full flex flex-col gap-4">
              <CommentItem
                name="John Doe"
                comment="Lorem ipsum dolor sit amet."
                date={new Date()}
              />
              <CommentItem
                name="John Doe"
                comment="Lorem ipsum dolor sit amet."
                date={new Date()}
              />
              <CommentItem
                name="John Doe"
                comment="Lorem ipsum dolor sit amet."
                date={new Date()}
              />
              <CommentItem
                name="John Doe"
                comment="Lorem ipsum dolor sit amet."
                date={new Date()}
              />
            </div>
          </div> */}
          {/* <CommentForm /> */}
        </div>
      )}
    </Suspense>
  );
}

export default ArticleContent;
