'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import BlogCardsPanel from '../blog_cards_panel';

import { ArticleBrief } from '@/models/articlesData';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { BriefData } from '@/models/BriefData';
import { useTranslations } from 'next-intl';
import CategoryFilter from '../category_filter';
import RecentPanel from '../recent_panel';
import SearchBox from '../search_box';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface IContentPanel {
  articles: ArticleBrief[];
  categories: BriefData[];
  totalPages: number;
  currentPage: number;
  currentFilter: string;
}

function ContentPanel({
  articles,
  categories,
  totalPages,
  currentPage,
  currentFilter,
}: IContentPanel) {
  const t = useTranslations('Components.blogPage');
  const router = useRouter();
  const [filter, setFilter] = useState(currentFilter || 'All');

  const handleFilterChange = (value: string) => {
    setFilter(value);
    router.push(`/blog?filter=${value}&page=1`);
  };

  const handlePageChange = (page: number) => {
    router.push(`/blog?filter=${filter}&page=${page}`);
  };

  const recentArticles =
    articles
      ?.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      )
      .slice(0, 3) || [];

  return (
    <section className=" w-full self-center flex flex-col px-5 lg:px-10 xl:px-0    md:min-w-[750px] lg:min-w-[950px] xl:min-w-[1100px] 2xl:min-w-[1300px]  ">
      <div className="w-full flex  flex-col-reverse lg:flex-row gap-16   ">
        {articles.length > 0 ? (
          <BlogCardsPanel title={'All blog articles '} list={articles} />
        ) : (
          <div className="w-full flex justify-center  items-center ">
            <Card className="w-full max-w-md  relative gap-0  ">
              <div className="h-14 w-[2px] bg-secondary absolute top-0 left-[25px]  "></div>
              <CardHeader className="relative pb-2">
                <h2 className="text-2xl font-normal  tracking-wider   border-l-secondary pl-8">
                  {t('Articles')}
                </h2>
              </CardHeader>
              <CardContent className="">
                <div className="text-center py-4 px-8 ">
                  <p className="text-lg text-muted-foreground">
                    {t('noArticles')}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
        <div className="w-full flex flex-col gap-5 lg:max-w-xs">
          <div className="w-full flex flex-col md:flex-row-reverse lg:flex-col  justify-end gap-5">
            <SearchBox />
            <CategoryFilter
              categories={categories}
              handleFilterChange={handleFilterChange}
            />
          </div>
          <RecentPanel articles={recentArticles} />
        </div>
      </div>
      <div className="w-full flex justify-between items-center space-x-2  mt-32 ">
        <Button
          variant="outline"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="border-none shadow-none text-foreground hover:text-primary  pl-0 "
        >
          <ChevronLeft className="ml-2 h-4 w-4" />
          {t('previous')}
        </Button>
        <div className="flex flex-row  gap-3">
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <Button
              key={page}
              variant={'default'}
              className={cn(
                'rounded-full w-8 h-8',
                currentPage === page && 'bg-border   ',
              )}
              onClick={() => handlePageChange(page)}
            >
              {page}
            </Button>
          ))}
        </div>
        <Button
          variant="outline"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="border-none shadow-none text-foreground hover:text-primary pr-0 "
        >
          {t('next')}
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </section>
  );
}

export default ContentPanel;
