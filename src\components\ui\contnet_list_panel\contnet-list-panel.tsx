'use client';
import { cn } from '@/lib/utils';
import ExtendedProductItem from '../extended_product_item';
import { IExtendedProductItem } from '../extended_product_item/extended-product-item';

interface IContentListPanel {
  products: IExtendedProductItem[];
}

function ContentListPanel({ products }: IContentListPanel) {
  return (
    <section className="w-full flex flex-col gap-10 items-center   ">
      {/* <ContentFilterPanel /> */}
      <div
        className={cn(
          'w-full  grid grid-cols-1 tb:grid-cols-2 md:grid-cols-4 gap-x-10 gap-y-16  place-items-center',
        )}
      >
        {products.map((item, index) => (
          <ExtendedProductItem
            key={index}
            name={item.name}
            code={item.code}
            description={item.description}
            image={item.image}
          />
        ))}
      </div>
    </section>
  );
}

export default ContentListPanel;
