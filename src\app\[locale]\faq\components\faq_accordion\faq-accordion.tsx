import { Accordion } from '@/components/ui/accordion';
import FaqItem from '../faq_item';

interface IFaqAccordion {
  questions: { question: string; answer: string; id: string }[];
}

function FaqAccordion({ questions }: IFaqAccordion) {
  return (
    <div className="w-full pr-4     ">
      <Accordion type="single" collapsible className="w-full">
        {questions.map((item, index) => (
          <FaqItem
            key={item.id}
            question={item.question}
            answer={item.answer}
            index={index}
          />
        ))}
      </Accordion>
    </div>
  );
}

export default FaqAccordion;
