export const formatTimeRange = (
  startTime: string,
  endTime: string,
  local: string,
) => {
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');

    if (local === 'fr') {
      if (time === '12:00') {
        return 'midi';
      }
      return `${hours}h${minutes}`;
    } else {
      if (time === '12:00') {
        return 'noon';
      }
      return `${hours}:${minutes}`;
    }
  };

  const start = formatTime(startTime);
  const end = formatTime(endTime);

  return `${start} ${local === 'en' ? 'to' : 'à'} ${end}`;
};
