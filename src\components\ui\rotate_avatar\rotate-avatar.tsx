import Image from 'next/image';
import './style/index.scss';
import { cn } from '@/lib/utils';
import { ImageData } from '@/models/ImageData';
interface IRotateAvatar {
  className?: string;
  title: string;
  image: ImageData;
}

function RotateAvatar({ className, title, image }: IRotateAvatar) {
  return (
    <div
      className={cn(
        'w-full flex flex-col items-center justify-center gap-4   ',
        className,
      )}
    >
      <div
        className={cn(
          'w-[240px] max-w-[240px] h-[240px] max-h-[240px]  rounded-full overflow-hidden  ',
          'sm:w-[415px] sm:max-w-[415px] sm:h-[415px] sm:max-h-[415px]    ',
          'md:w-[240px] md:max-w-[240px] md:h-[240px] md:max-h-[240px]  ',
          'xl:w-[275px] xl:max-w-[275px] xl:h-[275px] xl:max-h-[275px]  ',
          'rotate-avatar  w-full flex flex-col items-center justify-center relative rounded-full',
          "before:content-[''] before:absolute before:top-0 before:left-0 before:right-0 before:bottom-0 ",
          'before:rounded-full  before:border-[13px] before:border-solid  before:border-[#e6e5e5] before:w-full before:h-full before:z-10 ',
          ' before:transition-all before:duration-500 before:ease-in-out ',
          "after:content-[''] after:absolute after:top-0 after:left-0 after:right-0 after:bottom-0 ",
          'after:rounded-full  after:border-[13px] after:border-solid   after:w-full after:h-full after:z-20 ',
          'after:border-t-primary after:border-r-[#e6e5e5] after:border-b-[#e6e5e5] after:border-l-primary ',
          'after:transform after:rotate-[-10px] after:transition-all after:duration-700 after:ease-in-out ',
          'hover:after:rotate-[350deg]  ',
        )}
      >
        <Image
          src={image.path}
          alt={image.alt ?? `${title} image`}
          fill
          className="object-cover h-full w-full rounded-full overflow-hidden "
        />
      </div>
      <p
        className={cn(
          'text-[20px] leading-[28px] font-bold text-black hover:text-primary transition-all duration-700 ease-in-out relative ',
          "after:content-[''] after:absolute  after:left-[50%] after:translate-x-[-50%] after:right-0 after:bottom-[-15px]   after:w-[30px]  ",
          'after:h-[2px] after:bg-black after:transition-all after:duration-700 after:ease-in-out',
          'after:',
        )}
      >
        {title}
      </p>
    </div>
  );
}

export default RotateAvatar;
