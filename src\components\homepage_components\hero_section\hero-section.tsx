'use client';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { heroTitle, heroSubtitle, sectionContainer } from '@/lib/animations';

interface HeroSectionProps {
  backgroundImage?: string;
  title?: string;
  subtitle?: string;
  className?: string;
}

export default function HeroSection({
  backgroundImage = '/trade-show-environment.jpg',
  title = 'Trade Show Event Services from Edmonton to Vancouver',
  subtitle = 'Full-service display contractors.',
  className = '',
}: HeroSectionProps) {
  return (
    <motion.section
      className={`relative w-full min-h-[90vh] flex items-center justify-center overflow-hidden ${className}`}
      variants={sectionContainer}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
    >
      {/* Background Image with Overlay */}
      <div className="absolute inset-0 z-0">
        <Image
          src={backgroundImage}
          alt="GOODKEY SHOW SERVICES LTD. Trade Show Environment"
          fill
          className="object-cover"
          priority
          unoptimized
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/50 to-black/60"></div>
      </div>

      {/* Content */}
      <div className="container relative z-10 px-4 md:px-6 text-center">
        <div className="max-w-4xl mx-auto space-y-10">
          <motion.h1
            className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl lg:text-7xl text-white"
            variants={heroTitle}
          >
            {title}
          </motion.h1>
          <motion.p
            className="text-xl md:text-5xl text-white/80 max-w-3xl mx-auto"
            variants={heroSubtitle}
          >
            {subtitle}
          </motion.p>
        </div>
      </div>

      {/* Bottom Gradient */}
      <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-black to-transparent z-10"></div>
    </motion.section>
  );
}
