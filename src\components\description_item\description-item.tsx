interface IDescriptionItem {
  label: string;
  value: string;
}

function DescriptionItem({ label, value }: IDescriptionItem) {
  return (
    <div className="flex flex-row justify-between px-6 self-stretch py-1 bg-[#e6e6e6]">
      <p className="text-md text-left w-1/3 font-semibold">{label}</p>
      <p className="text-md text-left w-2/3  font-medium">{value}</p>
    </div>
  );
}

export default DescriptionItem;
