'use client';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Mail, MapPin, Phone } from 'lucide-react';

interface IContactInfo {}

function ContactInfo({}: IContactInfo) {
  const t = useTranslations('Components.Contact');
  const c = useTranslations('Components.common');

  return (
    <div className="w-full flex flex-col gap-10  pt-16   text-center ">
      <div className="grid grid-cols-1 lg:grid-cols-3 place-items-center  gap-6 xl:gap-4 w-full self-center max-w-[1250px]   ">
        <Link
          href="https://maps.google.com/?q=3%20Place%20Ville%20Marie%2C%20Suite%20400%2C%20Montreal%20QC%20H3B%202E3"
          target="_blank"
          rel="noopener noreferrer"
          className="bg-white h-[230px] w-full max-w-[400px] border border-black p-4 rounded-lg text-foreground shadow-lg flex flex-col items-center justify-center transition-all duration-500 hover:scale-105"
        >
          <MapPin className="w-8 h-8 mb-8 text-secondary" />
          <p className="text-lg">1405, Boulevard Hymus </p>
          <p className="text-lg">Dorval, QC H9P 1J5</p>
        </Link>
        <Link
          href="tel:5146947945"
          className="bg-white h-[230px] w-full max-w-[400px] border border-black p-4 rounded-lg text-foreground shadow-lg flex flex-col items-center justify-center transition-all duration-500 hover:scale-105"
        >
          <Phone className="w-8 h-8 mb-8 text-secondary" />
          <p className="text-lg font-semibold  ">Phone</p>
          <p className="text-lg text-foreground">************</p>
        </Link>
        <Link
          href="mailto:<EMAIL>"
          className="bg-white h-[230px] w-full  max-w-[400px]  border border-black p-4 rounded-lg text-foreground shadow-lg flex flex-col items-center justify-center transition-all duration-500 hover:scale-105"
        >
          <Mail className="w-8 h-8 mb-8 text-secondary" />
          <p className="text-xs text-foreground/70  mb-2 ">
            {t('emailCardNote')}
          </p>
          <p className="text-lg text-foreground"><EMAIL></p>
        </Link>
      </div>
    </div>
  );
}

export default ContactInfo;
