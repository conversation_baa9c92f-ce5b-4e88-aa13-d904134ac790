'use client';
import { cn } from '@/lib/utils';
import { ImageData } from '@/models/ImageData';
import Image from 'next/image';
import { ReactNode, useRef } from 'react';
import { motion, useInView } from 'framer-motion';

interface ISideImageDescription {
  image: ImageData;
  Content?: ReactNode;
  leftComponent?: ReactNode;
  contentClassName?: string;
  imageClassName?: string;
  className?: string;
  reverse?: boolean;
}

function SideImageDescription({
  image,
  Content,
  leftComponent,
  contentClassName,
  imageClassName,
  className,
  reverse,
}: ISideImageDescription) {
  const imageRef = useRef(null);
  const contentRef = useRef(null);

  const imageInView = useInView(imageRef, {
    once: false,
    amount: 0.3,
  });
  const contentInView = useInView(contentRef, {
    once: false,
    amount: 0.3,
  });

  const imageVariants = {
    hidden: { opacity: 0, x: reverse ? 100 : -100 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.7, ease: 'easeInOut' },
    },
  };

  const contentVariants = {
    hidden: { opacity: 0, x: reverse ? -100 : 100 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.7, ease: 'easeInOut' },
    },
  };

  return (
    <div
      className={cn(
        'flex flex-col xl:flex-row justify-between xl:gap-12 self-center w-full overflow-hidden ',
        reverse ? 'xl:flex-row-reverse' : 'xl:flex-row',
        className,
      )}
    >
      <motion.div
        ref={imageRef}
        initial="hidden"
        animate={imageInView ? 'visible' : 'hidden'}
        variants={imageVariants}
        className="flex flex-col justify-center items-center min-w-[50%] w-full xl:w-auto h-[400px] md:h-[600px] relative"
      >
        <Image
          src={image.path}
          alt={image.alt ?? 'image'}
          fill={true}
          className={cn('object-cover w-full h-full', imageClassName)}
        />
        {leftComponent && (
          <div className="absolute top-[-15px] left-[-15px] md:top-[-25px] md:left-[-25px]">
            {leftComponent}
          </div>
        )}
      </motion.div>
      <motion.div
        ref={contentRef}
        initial="hidden"
        animate={contentInView ? 'visible' : 'hidden'}
        variants={contentVariants}
        className={cn(
          'flex flex-col justify-start items-center w-full',
          contentClassName,
        )}
      >
        {Content}
      </motion.div>
    </div>
  );
}

export default SideImageDescription;
