'use client';

import type { DropdownMenuProps } from '@radix-ui/react-dropdown-menu';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  useOpenState,
} from './dropdown-menu';
import { Too<PERSON>barButton } from './toolbar';
import {
  focusEditor,
  getMark,
  setMarks,
  useEditorState,
} from '@udecode/plate-common';
import { cn } from '@/lib/utils';
import { ImFont } from 'react-icons/im';

const FONT_FAMILIES = [
  'Inter',
  'Arial',
  'Roboto',
  'Helvetica',
  'Times New Roman',
  'Comic Neue',
  'IBM Plex Sans',
  'Lora',
  'Edu AU VIC WA NT Hand',
];

export function FontFamilyDropdownMenu(props: DropdownMenuProps) {
  const editor = useEditorState();
  const openState = useOpenState();

  return (
    <DropdownMenu modal={false} {...openState} {...props}>
      <DropdownMenuTrigger asChild>
        <ToolbarButton pressed={openState.open} tooltip="Font Family">
          {(getMark(editor, 'fontFamily') as string) || (
            <ImFont className={cn('max-w-4 max-h-4')} />
          )}
        </ToolbarButton>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align="start"
        className="flex max-h-[500px] min-w-[180px] flex-col gap-0.5 overflow-y-auto"
      >
        {FONT_FAMILIES.map((fontFamily) => (
          <DropdownMenuItem
            key={fontFamily}
            onSelect={() => {
              setMarks(editor, { fontFamily });
              focusEditor(editor);
            }}
          >
            {fontFamily}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
