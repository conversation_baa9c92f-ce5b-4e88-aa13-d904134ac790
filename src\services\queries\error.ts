type Error = {
  message: string;
  errorCode: number;
};
export class AppError extends Error {
  constructor(message: string, errorCode: number) {
    super(JSON.stringify({ message, errorCode }));
    this.name = 'AppError';
  }

  static parse(errorMessage: string): Error | null {
    try {
      const parsedError = JSON.parse(errorMessage);
      if (typeof parsedError === 'object' && parsedError !== null) {
        return parsedError;
      }
      return null;
    } catch (error) {
      return null;
    }
  }
}
