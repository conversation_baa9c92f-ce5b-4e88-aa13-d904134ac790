import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { ArticleBrief } from '@/models/articlesData';
import RecentArticle from '../../[slug]/components/recent_article';

interface IRecentPanel {
  articles: ArticleBrief[];
}

function RecentPanel({ articles }: IRecentPanel) {
  return (
    <Card className="w-full  rounded-xl  h-fit hidden xl:flex xl:max-w-xs px-4 border-t-[2px] border-t-secondary relative bg-gray-50  ">
      <div className="h-14 w-[2px] bg-secondary absolute top-0 left-[25px]  "></div>
      <CardHeader className="pl-8">
        <CardTitle className="text-2xl font-normal  tracking-wider   border-l-secondary ">
          Recent Articles
        </CardTitle>
      </CardHeader>
      <CardContent className="grid grid-cols-1 tb:grid-cols-2 md:grid-cols-3 xl:grid-cols-1  gap-x-6 gap-y-10 xl:gap-y-6 px-2  pb-10">
        {articles.map((article, index) => (
          <RecentArticle
            key={article.id}
            isLast={index === articles.length - 1}
            title={article.name}
            createdAt={new Date(article.createdAt)}
            id={article.id}
            image={article?.image}
            slug={article.slug}
          />
        ))}
      </CardContent>
    </Card>
  );
}

export default RecentPanel;
