import { contactFormSchema } from '@/app/[locale]/contact-us/components/contact_form/contact-form';
import fetcher from './fetcher';
import { z } from 'zod';

import { JobApplicationFormSchema } from '@/app/[locale]/career/components/job_application_form/job-application-form';
import { AppointmentDetails, QuoteRequest } from '@/models/RequestData';

const RequestQuery = {
  tags: ['Contact'] as const,
  SendContactMessage: async (data: z.infer<typeof contactFormSchema>) =>
    fetcher<boolean>(`requests/contact`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ...data }),
    }),

  SendSurveyRequest: (uuid: string) => async (data: AppointmentDetails) =>
    fetcher<number>(`requests/Quote/${uuid}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ...data }),
    }),
  getQuoteRequest: async (uuid: string) =>
    fetcher<QuoteRequest>(`requests/Quote/${uuid}`),

  subscribe: async (email: string) =>
    fetcher<boolean>(`requests/subscribe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(email),
    }),

  SendCareerRequest: async ({
    CV,
    otherDoc,
    ...data
  }: z.infer<typeof JobApplicationFormSchema>) => {
    const formData = new FormData();
    formData.append('CV', CV);
    if (otherDoc) formData.append('otherDoc', otherDoc);

    formData.append('firstName', data.firstName);
    formData.append('lastName', data.lastName);
    formData.append('email', data.email);
    formData.append('phoneNumber', data.phoneNumber);
    if (data.message) formData.append('message', data.message);
    // formData.append('requestType', '1');
    return fetcher(
      `requests/sendcareer`,
      {
        method: 'POST',
        body: formData,
      },
      true,
    );
  },
};
export default RequestQuery;
