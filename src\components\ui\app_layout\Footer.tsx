'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Button } from '../button';
import {
  FaFacebook,
  FaInstagram,
  FaTwitter,
  FaLinkedin,
} from 'react-icons/fa6';
import { MapPin, Phone, Mail } from 'lucide-react';
import { MenuItem } from '@/models/types';

export default function Footer({ items }: { items: MenuItem[] }) {
  return (
    <footer
      id="contact"
      className="w-full py-12 md:py-24 bg-gray-900 text-white"
    >
      <div className="container px-4 md:px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div>
            <div className="mb-6 bg-white w-fit ">
              <Image
                src="/gSS-logo-main.png"
                alt="GOODKEY SHOW SERVICES LTD. Logo"
                width={300}
                height={110}
                className="h-20 w-auto"
                unoptimized
              />
            </div>
            <p className="text-gray-400 mb-4">
              Goodkey Show Services Ltd. is one of Western Canada's most
              experienced full-service display contractors.
            </p>
            <div className="flex space-x-4">
              <Link
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <FaFacebook className="h-5 w-5" />
                <span className="sr-only">Facebook</span>
              </Link>
              <Link
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <FaTwitter className="h-5 w-5" />
                <span className="sr-only">Twitter</span>
              </Link>
              <Link
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <FaInstagram className="h-5 w-5" />
                <span className="sr-only">Instagram</span>
              </Link>
              <Link
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <FaLinkedin className="h-5 w-5" />
                <span className="sr-only">LinkedIn</span>
              </Link>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-3">
              {items.map((item) => (
                <li key={item.id}>
                  <Link
                    href={item.href}
                    target={item.target || undefined}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Head Office</h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <MapPin className="h-5 w-5 mr-2 mt-0.5 text-[#00646C]" />
                <span className="text-gray-400">
                  5506 - 48 Street NW
                  <br /> Edmonton, Alberta, Canada T6B 2Z1
                </span>
              </li>
              <li className="flex items-center">
                <Phone className="h-5 w-5 mr-2 text-[#00646C]" />
                <span className="text-gray-400">**************</span>
              </li>
              <li className="flex items-center">
                <Mail className="h-5 w-5 mr-2 text-[#00646C]" />
                <span className="text-gray-400"><EMAIL></span>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Get In Touch</h3>
            <form className="space-y-4">
              <div>
                <input
                  type="text"
                  placeholder="Name"
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-[#00646C] focus:border-transparent"
                />
              </div>
              <div>
                <input
                  type="email"
                  placeholder="Email"
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-[#00646C] focus:border-transparent"
                />
              </div>
              <div>
                <textarea
                  placeholder="Message"
                  rows={3}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-[#00646C] focus:border-transparent"
                ></textarea>
              </div>
              <Button className="w-full bg-[#B10055] hover:bg-[#B10055]/90 text-white">
                Send Message
              </Button>
            </form>
          </div>
        </div>
        <div className="flex flex-col gap-4  md:flex-row md:justify-between  border-t border-gray-800 mt-12 pt-8 text-center text-gray-400 text-sm">
          <p>
            © {new Date().getFullYear()} GOODKEY SHOW SERVICES LTD. All rights
            reserved.
          </p>

          <div className="flex flex-col md:flex-row gap-2 items-center justify-center  ">
            <Link href="/privacy-policy" className="  hover:text-secondary">
              PRIVACY POLICIES
            </Link>
            <div className="hidden md:flex   md:h-3 border border-gray-400  "></div>
            <Link
              href="/terms-and-conditions"
              className="  hover:text-secondary"
            >
              TERMS AND CONDITIONS
            </Link>
          </div>

          <Link
            href="https://www.malopan.com"
            target="_blank"
            rel="noreferrer"
            className="  hover:text-secondary"
          >
            Powered by Malopan Communications Inc
          </Link>
        </div>
      </div>
    </footer>
  );
}
