import { cn } from '@/lib/utils';
import { ImageData } from '@/models/ImageData';
import Link from 'next/link';
import { UrlObject } from 'url';
import AppImage from '../ui/app_image';

interface ICardItem {
  image?: ImageData;
  title?: string;
  description?: string;
  className?: string;
  href?: string | UrlObject;
}

function CardItem({ image, description, title, className, href }: ICardItem) {
  return (
    <>
      {href ? (
        <Link
          href={href}
          className={cn(
            'flex flex-col items-center gap-2 cursor-pointer  overflow-hidden ',
            className,
          )}
        >
          <AppImage
            legacy
            image={image}
            className=" lg:max-w-[520px] lg:max-h-[350px]"
          />
          {(title || description) && (
            <div className=" flex flex-col gap-4 text-black text-center md:text-left lg:text-left lg:pb-5 lg:max-w-[520px]">
              {title && (
                <h3 className="text-xl font-bold  hover:text-red-500 ">
                  {title}
                </h3>
              )}
              {description && (
                <p className="text-base font-medium ">{description}</p>
              )}
            </div>
          )}
        </Link>
      ) : (
        <div
          className={cn(
            'flex flex-col items-center gap-2  overflow-hidden ',
            className,
          )}
        >
          <AppImage
            legacy
            image={image}
            className=" lg:max-w-[520px] lg:max-h-[350px]"
          />
          {(title || description) && (
            <div className=" flex flex-col gap-4 text-black text-center md:text-left lg:text-left lg:pb-5 lg:max-w-[520px]">
              {title && (
                <h3 className="text-xl font-bold  hover:text-red-500 ">
                  {title}
                </h3>
              )}
              {description && (
                <p className="text-base font-medium ">{description}</p>
              )}
            </div>
          )}
        </div>
      )}
    </>
  );
}

export default CardItem;
