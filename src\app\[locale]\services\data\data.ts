import { ImageData } from '@/models/ImageData';

interface Service {
  titleKey: string;
  description: string;
  images: ImageData[];
  slug: string;
}

export const services: Service[] = [
  {
    titleKey: 'service1Title',
    description: 'Fast and efficient package handling services',
    images: [
      {
        path: '/services/single-service-image-1.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
      {
        path: '/services/single-service-image-2.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
      {
        path: '/services/single-service-image-3.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
    ],
    slug: 'north-american-van-lines',
  },
  {
    titleKey: 'service2Title',
    description: 'Advanced scanning and tracking technology',
    images: [
      {
        path: '/services/single-service-image-1.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
      {
        path: '/services/single-service-image-2.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
      {
        path: '/services/single-service-image-3.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
    ],
    slug: 'usa-canada',
  },
  {
    titleKey: 'service3Title',
    description: 'Comprehensive inventory control services',
    images: [
      {
        path: '/services/single-service-image-1.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
      {
        path: '/services/single-service-image-2.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
      {
        path: '/services/single-service-image-3.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
    ],
    slug: 'international',
  },
  {
    titleKey: 'service4Title',
    description: 'Professional warehouse management solutions',
    images: [
      {
        path: '/services/single-service-image-1.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
      {
        path: '/services/single-service-image-2.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
      {
        path: '/services/single-service-image-3.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
    ],
    slug: 'local-moves',
  },
  {
    titleKey: 'service5Title',
    description: 'Efficient distribution network solutions',
    images: [
      {
        path: '/services/single-service-image-1.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
      {
        path: '/services/single-service-image-2.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
      {
        path: '/services/single-service-image-3.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
    ],
    slug: 'packing-services',
  },
  {
    titleKey: 'service6Title',
    description: 'End-to-end supply chain management',
    images: [
      {
        path: '/services/single-service-image-1.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
      {
        path: '/services/single-service-image-2.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
      {
        path: '/services/single-service-image-3.jpg',
        alt: 'placeholder',
        name: 'placeholder',
      },
    ],
    slug: 'storage-services',
  },
];
