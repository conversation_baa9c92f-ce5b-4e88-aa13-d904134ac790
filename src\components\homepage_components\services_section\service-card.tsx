interface ServiceCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  className?: string;
}

export default function ServiceCard({
  icon,
  title,
  description,
  className = '',
}: ServiceCardProps) {
  return (
    <div
      className={`bg-[#004B4B]/50 p-8 rounded-lg border border-[#ffffff20] hover:border-[#ffffff40] transition-all ${className}`}
    >
      <div className="mb-6 text-[#4ECDC4]">{icon}</div>
      <h3 className="text-xl font-semibold mb-2 text-white">{title}</h3>
      <p className="text-white/80">{description}</p>
    </div>
  );
}
