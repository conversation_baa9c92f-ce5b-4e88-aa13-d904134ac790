import * as React from 'react';

import { cn } from '@/lib/utils';
import { useFormField } from './form';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    const { error } = useFormField();
    return (
      <input
        type={type}
        className={cn(
          'flex  w-full  bg-transparent text-foreground  text-sm  transition-colors  placeholder:text-muted-foreground focus-visible:outline-none  focus-visible:border-primary/70 disabled:cursor-not-allowed disabled:opacity-50',
          '  px-2 py-3  border border-gray-400 rounded-lg',
          error && 'border-red-500 ',
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
Input.displayName = 'Input';

export { Input };
