import { format } from 'date-fns';
import './style/index.scss';
interface ICommentItem {
  name: string;
  comment: string;
  date: Date;
}

function CommentItem({ name, comment, date }: ICommentItem) {
  return (
    <div className="relative grid grid-cols-1 gap-4 p-4  border rounded-lg bg-white shadow-lg">
      <div className="relative flex gap-4">
        <div className="flex flex-col w-full">
          <div className="flex flex-row justify-between">
            <p className="relative text-xl whitespace-nowrap truncate overflow-hidden">
              {name}
            </p>
            <a className="text-gray-500 text-xl" href="#">
              <i className="fa-solid fa-trash"></i>
            </a>
          </div>
          <p className="text-gray-400 text-sm">
            {format(date, "dd MMMM yyyy, 'at' HH:mm a")}
          </p>
        </div>
      </div>
      <p className="-mt-4 text-gray-500">{comment}</p>
    </div>
  );
}

export default CommentItem;
