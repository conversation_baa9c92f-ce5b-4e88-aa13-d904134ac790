'use client';
import {
  GoogleMap,
  useJs<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Info<PERSON>indow,
  <PERSON><PERSON>,
} from '@react-google-maps/api';
import { useCallback, useState } from 'react';

interface IContactMap {}

function ContactMap({}: IContactMap) {
  // const { data: locationsData, isSuccess: locationsSuccess } = useQuery({
  //   queryKey: ['locations'],
  //   queryFn: async () => {
  //     const location = await fetchGeoLocation(
  //       '3296 Boul Bécancour, Bécancour, QC G9H 3W2',
  //     );
  //     return location;
  //   },
  // });
  // console.log('locations', locationsData);
  const [openInfoWindow, setOpenInfoWindow] = useState(false);

  const { isLoaded } = useJsApiLoader({
    id: 'google-map-script',
    googleMapsApiKey: process.env.NEXT_PUBLIC_Google_API_Key!,
  });

  const [map, setMap] = useState<google.maps.Map | null>(null);

  const onLoad = useCallback(function callback(mapInstance: google.maps.Map) {
    setMap(mapInstance);
  }, []);

  const onUnmount = useCallback(function callback(
    mapInstance: google.maps.Map,
  ) {
    setMap(null);
  }, []);

  return isLoaded ? (
    <div className="h-[400px] w-full ">
      <GoogleMap
        mapContainerStyle={{ width: '100%', height: '100%' }}
        center={{
          lat: 45.48636659760768,
          lng: -73.771050374036,
        }}
        zoom={15}
        onLoad={onLoad}
        onUnmount={onUnmount}
      >
        <Marker
          position={{
            lat: 45.48636659760768,
            lng: -73.771050374036,
          }}
          onClick={() => setOpenInfoWindow(true)}
        >
          {openInfoWindow && (
            <InfoWindow onCloseClick={() => setOpenInfoWindow(false)}>
              <div className="px-5  py-2 ">
                <h3 className="text-lg font-bold">John Gray Moving</h3>
                <span className="text-wrap text-center text-sm leading-6 text-primary font-medium ">
                  1405, Boulevard Hymus
                  <br />
                  Dorval, QC H9P 1J5
                </span>
              </div>
            </InfoWindow>
          )}
        </Marker>
      </GoogleMap>
    </div>
  ) : (
    <></>
  );
}

export default ContactMap;
