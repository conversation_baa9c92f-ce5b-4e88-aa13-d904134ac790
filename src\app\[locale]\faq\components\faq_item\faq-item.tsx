import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { ChevronDown } from 'lucide-react';

interface IFaqItem {
  question: string;
  answer: string;
  index: number;
}

function FaqItem({ question, answer, index }: IFaqItem) {
  return (
    <AccordionItem className="mb-5" value={`item-${index}`}>
      <AccordionTrigger className="text-xl font-normal text-left py-5 px-8  bg-[#D9D9D9]   data-[state=open]:rounded-b-none  [&[data-state=open]>svg]:ml-4  rounded-lg   ">
        <div className="bg-secondary size-6 rounded-full flex items-center justify-center text-white p-5  ">
          {index + 1}
        </div>
        {question}
        <ChevronDown className="h-6 w-6 shrink-0 transition-transform duration-200" />
      </AccordionTrigger>
      <AccordionContent className="text-lg bg-[#D9D9D9] rounded-lg  rounded-t-none ">
        <p
          className=" ml-16 mr-20 pt-2 pb-5 text-gray-600 mb-16  leading-9"
          dangerouslySetInnerHTML={{ __html: answer }}
        ></p>
      </AccordionContent>
    </AccordionItem>
  );
}

export default FaqItem;
