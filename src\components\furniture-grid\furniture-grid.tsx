import Image from 'next/image';
import { cn } from '@/lib/utils';

interface FurnitureCardProps {
  item: FurnitureItem;
  className?: string;
}

const FurnitureCard = ({ item, className }: FurnitureCardProps) => {
  return (
    <div
      className={cn(
        'flex flex-col items-center  p-4',
        ' transition-all duration-200',
        'group cursor-pointer',
        className,
      )}
    >
      <div className="w-full aspect-square  flex items-center justify-center  overflow-hidden">
        <Image
          src={item.image}
          alt={item.alt || `${item.name} ${item.subtitle}`}
          width={200}
          height={200}
          className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-200"
          priority={false}
        />
      </div>
      <div className="text-center">
        <h3 className="text-base font-semibold text-[#102D47] mb-1 group-hover:text-brand-primary transition-colors">
          {item.name}
        </h3>
        <p className="text-sm text-[#102D47]  ">{item.subtitle}</p>
      </div>
    </div>
  );
};

export interface FurnitureItem {
  id: string;
  name: string;
  subtitle: string;
  image: string;
  alt?: string;
}

interface FurnitureGridProps {
  items: FurnitureItem[];
  className?: string;
}

const FurnitureGrid = ({ items, className }: FurnitureGridProps) => {
  const gridClasses = cn(
    'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 max-w-6xl mx-auto',
  );

  return (
    <div className={cn('w-full px-4 container mx-auto  ', className)}>
      <div className={gridClasses}>
        {items.map((item) => (
          <FurnitureCard key={item.id} item={item} />
        ))}
      </div>
    </div>
  );
};

export default FurnitureGrid;
