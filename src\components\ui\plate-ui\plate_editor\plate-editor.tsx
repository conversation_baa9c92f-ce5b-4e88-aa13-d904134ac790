'use client';
import { createPlateEditor, Plate } from '@udecode/plate-common';
import { Editor } from '../editor';
import { plugins } from './plugins/plugins';
import { useMemo } from 'react';
import { ELEMENT_H1 } from '@udecode/plate-heading';

interface IPlateEditor {
  value: string | Node[];
}
const initialValueJson = JSON.stringify([
  {
    type: ELEMENT_H1,
    level: 1,
    children: [{ text: 'Content will be available soon...' }],
  },
]);
function PlateEditor({ value }: IPlateEditor) {
  console.log('value', value);
  const editor = createPlateEditor({ plugins });

  const deserialize = (v: string | Node[]): Node[] => {
    if (typeof v === 'string') {
      return JSON.parse(v);
    }
    return v;
  };

  const deserializedInitialValue = useMemo(() => {
    try {
      return deserialize(value);
    } catch (error) {
      return deserialize(initialValueJson);
    }
  }, [value]);

  return (
    <Plate
      plugins={plugins}
      editor={editor}
      initialValue={deserializedInitialValue as any}
      readOnly
    >
      <Editor />
    </Plate>
  );
}

export default PlateEditor;
