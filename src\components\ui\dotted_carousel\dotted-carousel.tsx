'use client';
import React, { Children, ReactNode, useState } from 'react';
import Autoplay from 'embla-carousel-autoplay';
import { cn } from '@/lib/utils';
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from '../carousel';
import Fade from 'embla-carousel-fade';

interface IDottedCarousel {
  children: ReactNode;
  className?: string;
  dotsClassName?: string;
  dotted?: boolean;
  full?: boolean;
}

function DottedCarousel({
  children,
  className,
  dotted = true,
  full = true,
  dotsClassName,
}: IDottedCarousel) {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);

  React.useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on('select', () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);
  return (
    <Carousel
      className={cn('relative h-fit  ', className)}
      setApi={setApi}
      plugins={[
        Autoplay({
          delay: 8000,
          ...(full ? [Fade()] : []),
        }),
      ]}
      opts={{
        align: 'center',
        loop: true,
      }}
    >
      <CarouselContent>
        {Children.toArray(children)?.map((item, index) => (
          <CarouselItem key={index}>{item}</CarouselItem>
        ))}
      </CarouselContent>
      {dotted && (
        <div
          className={cn(
            'flex items-center justify-center gap-3 absolute right-10 bottom-[100px] ',
            dotsClassName,
          )}
        >
          {[...Array(count)].map((_, index) => (
            <div
              key={index}
              className={cn(
                'h-[5px] w-6  bg-white transition-all duration-100 hover:bg-secondary cursor-pointer',
                index === current - 1 && 'bg-secondary',
              )}
              onClick={() => api?.scrollTo(index)}
            />
          ))}
        </div>
      )}
    </Carousel>
  );
}

export default DottedCarousel;
