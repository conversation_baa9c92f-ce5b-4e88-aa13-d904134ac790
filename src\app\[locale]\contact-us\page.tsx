import { Metadata, ResolvingMetadata } from 'next';

import MiddleContainer from '@/components/middle_container';
import ContactSection from './components/contact_section';
import PageBanner from '@/components/page_banner';
import ContactMap from './components/contact_map';
import { getTranslations } from 'next-intl/server';
import { headers } from 'next/headers';

export async function generateMetadata(
  { params: { locale } }: { params: { locale: string } },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const t = await getTranslations({
    locale,
    namespace: 'Components.Contact',
  });
  return {
    title: t('metaDataTitle'),
    description: t('metaDataDesc'),
    metadataBase: new URL(
      process.env.APP_BASE_URL ?? `https://${headers().get('host')}`,
    ),
  };
}

export default async function ContactUs() {
  const t = await getTranslations('Components.Contact');
  return (
    <main className="flex w-full flex-col gap-10 pb-32">
      <PageBanner
        title={t('bannerTitle')}
        background="linear-gradient(to right, #8e5c7d, #2d4c5c)"
        items={[{ title: t('bannerSubTitle') }]}
      />
      <ContactMap />
      <MiddleContainer>
        <ContactSection />
      </MiddleContainer>
    </main>
  );
}
