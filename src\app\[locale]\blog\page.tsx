import ContentPanel from './components/content_panel';
import fetcher from '@/services/queries/fetcher';
import { ArticleBrief } from '@/models/articlesData';
import { BriefData } from '@/models/BriefData';
import MiddleContainer from '@/components/middle_container';
import { getTranslations } from 'next-intl/server';
import { Metadata, ResolvingMetadata } from 'next';
import { headers } from 'next/headers';

export async function generateMetadata(
  { params: { locale } }: { params: { locale: string } },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const t = await getTranslations({
    locale,
    namespace: 'Components.blogPage',
  });
  return {
    title: t('metaDataTitle'),
    description: t('metaDataDesc'),
    metadataBase: new URL(
      process.env.APP_BASE_URL ?? `https://${headers().get('host')}`,
    ),
  };
}

const ITEMS_PER_PAGE = 6;

export default async function Blog({
  searchParams,
}: {
  searchParams: { page?: string; filter?: string; search?: string };
}) {
  const page = Number(searchParams.page) || 1;
  const filter = searchParams.filter || 'all';
  const search = searchParams.search || '';
  const articles = await fetcher<ArticleBrief[]>(`BlogArticles/getbrief`);
  const categories = await fetcher<BriefData[]>(`BlogCategories/getBrief`);

  const sortedArticles = articles.sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
  );

  const filteredArticles =
    search.length > 0 && filter === 'all'
      ? sortedArticles.filter(
          (article) =>
            article.name.toLowerCase().includes(search.toLowerCase()) ||
            article.tags
              ?.split(',')
              .some((tag) => tag.toLowerCase().includes(search.toLowerCase())),
        )
      : search.length > 0 && filter !== 'all'
        ? sortedArticles.filter(
            (article) =>
              (article.name.toLowerCase().includes(search.toLowerCase()) ||
                article.tags
                  ?.split(',')
                  .some((tag) =>
                    tag.toLowerCase().includes(search.toLowerCase()),
                  )) &&
              article.categories.some(
                (category) => category.id === Number(filter),
              ),
          )
        : filter === 'all'
          ? sortedArticles
          : sortedArticles.filter((article) =>
              article.categories.some(
                (category) => category.id === Number(filter),
              ),
            );

  // filter === 'all'
  //   ? sortedArticles
  //   : sortedArticles.filter((article) =>
  //       article.categories.some((category) => category.id === Number(filter)),
  //     );

  const totalPages = Math.ceil(filteredArticles.length / ITEMS_PER_PAGE);
  const paginatedArticles = filteredArticles.slice(
    (page - 1) * ITEMS_PER_PAGE,
    page * ITEMS_PER_PAGE,
  );

  // const latestArticles = paginatedArticles.slice(0, 4);
  // const oldArticles = paginatedArticles.slice(4);

  return (
    <div className="flex w-full flex-col gap-32  pt-12 pb-24  max-w-[1920px]  ">
      <MiddleContainer>
        <ContentPanel
          articles={paginatedArticles}
          categories={categories}
          totalPages={totalPages}
          currentPage={page}
          currentFilter={filter}
        />
      </MiddleContainer>
    </div>
  );
}
