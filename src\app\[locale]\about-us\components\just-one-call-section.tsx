'use client';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger, TabsContent } from '@/components/ui/tabs';
import { fadeInLeft, fadeInRight, sectionContainer } from '@/lib/animations';

interface TabContentData {
  id: string;
  title: string;
  description: string;
  imageSrc: string;
  imageAlt: string;
}

const tabsData: TabContentData[] = [
  {
    id: 'just-one-call',
    title: 'Just One Call',
    description:
      "At Goodkey Show Services Ltd., we'll handle everything for you from concept to clean up. This includes designing exhibit booths and specialty lighting to large format printing and providing rental furnishings. Just give us the word, and we'll take care of the rest.\n\nOur services are not restricted to the site alone. Over the years, we have established a number of key long-term relationships with our suppliers. These relationships allow us to provide you with complete bonded shipping services, custom brokering and worldwide exhibit planning - all with just one call.",
    imageSrc: '/cim-magazine-booth.jpeg',
    imageAlt: 'Trade show booth setup with professional exhibition services',
  },
  {
    id: 'we-can-really-move',
    title: 'We Can Really Move',
    description:
      'Our services are not restricted to the site alone. Over the years, we have established a number of key long-term relationships with our suppliers. These relationships allow us to provide you with complete bonded shipping services, custom brokering and worldwide exhibit planning - all with just one call.',
    imageSrc: '/snc-lavalin-booth.jpeg',
    imageAlt: 'Professional logistics and shipping services for trade shows',
  },
  {
    id: 'custom-made-easy',
    title: 'Custom Made Easy',
    description:
      'We specialize in creating custom exhibit solutions tailored to your specific needs. From unique booth designs to specialized displays, our team works closely with you to bring your vision to life with precision and creativity.',
    imageSrc: '/dux-mining-booth.jpeg',
    imageAlt: 'Custom trade show booth design and fabrication',
  },
  {
    id: 'get-connected',
    title: 'Get Connected',
    description:
      'Stay connected throughout your entire project with our comprehensive communication system. We provide regular updates, coordinate with all stakeholders, and ensure seamless collaboration from planning to execution.',
    imageSrc: '/axxis-bme-booth.jpeg',
    imageAlt: 'Professional communication and project coordination',
  },
  {
    id: 'one-budget-on-time',
    title: 'One Budget & On time',
    description:
      'We understand the importance of staying within budget and meeting deadlines. Our experienced project managers ensure your exhibit is delivered on time and within your specified budget, without compromising on quality.',
    imageSrc: '/about-us/sam_9238.jpg',
    imageAlt: 'On-time and on-budget trade show project delivery',
  },
  {
    id: 'mission-statement',
    title: 'Mission Statement',
    description:
      "Our mission is to provide exceptional trade show and exhibit services that exceed our clients' expectations. We are committed to delivering innovative solutions, maintaining the highest quality standards, and building lasting partnerships with our clients.",
    imageSrc: '/about-us/section-1-image.jpg',
    imageAlt: 'Goodkey Show Services mission and values',
  },
];

interface JustOneCallSectionProps {
  className?: string;
}

export default function JustOneCallSection({
  className = '',
}: JustOneCallSectionProps) {
  return (
    <motion.section
      className={`py-16 lg:py-24 container mx-auto w-full ${className}`}
      variants={sectionContainer}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
    >
      <div className="  w-full">
        <Tabs
          defaultValue={tabsData[0].id}
          className="flex flex-col lg:grid lg:grid-cols-[1fr_300px] gap-6 lg:items-start  w-full"
        >
          {/* Left Content - Dynamic based on selected tab */}
          <motion.div className="order-2 lg:order-1" variants={fadeInLeft}>
            {tabsData.map((tab) => (
              <TabsContent
                key={tab.id}
                value={tab.id}
                className="mt-0 space-y-0"
              >
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-8  items-start">
                  {/* Image */}
                  <motion.div
                    className="relative aspect-[4/3] w-full overflow-hidden rounded-lg"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.8, ease: 'easeOut' }}
                    whileHover={{ scale: 1.02 }}
                  >
                    <Image
                      src={tab.imageSrc}
                      alt={tab.imageAlt}
                      fill
                      className="object-cover transition-transform duration-300"
                      sizes="(max-width: 768px) 100vw, 50vw"
                    />
                  </motion.div>

                  {/* Content */}
                  <motion.div
                    className="space-y-8"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, ease: 'easeOut', delay: 0.2 }}
                  >
                    <motion.h3
                      className="text-xl sm:text-2xl md:text-3xl font-bold text-[#00646C]"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{
                        duration: 0.5,
                        ease: 'easeOut',
                        delay: 0.3,
                      }}
                    >
                      {tab.title}
                    </motion.h3>
                    <motion.div
                      className="text-[#0A0A0A] leading-relaxed text-sm sm:text-base space-y-3 sm:space-y-4"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{
                        duration: 0.5,
                        ease: 'easeOut',
                        delay: 0.4,
                      }}
                    >
                      {tab.description.split('\n\n').map((paragraph, index) => (
                        <p key={index}>{paragraph}</p>
                      ))}
                    </motion.div>
                  </motion.div>
                </div>
              </TabsContent>
            ))}
          </motion.div>

          {/* Tabs - Horizontal on mobile, vertical on desktop */}
          <motion.div
            className="order-1 lg:order-2"
            variants={fadeInRight}
            initial="hidden"
            animate="visible"
          >
            <motion.div
              initial="hidden"
              animate="visible"
              variants={{
                hidden: {},
                visible: {
                  transition: {
                    staggerChildren: 0.1,
                    delayChildren: 0.2,
                  },
                },
              }}
            >
              <div className="lg:hidden">
                {/* Mobile: Tabs with arrow navigation */}
                <div className="relative">
                  {/* Left Arrow */}
                  <button
                    className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-md hover:bg-white transition-all duration-200"
                    onClick={() => {
                      const container = document.getElementById(
                        'mobile-tabs-container',
                      );
                      if (container) {
                        container.scrollBy({ left: -200, behavior: 'smooth' });
                      }
                    }}
                  >
                    <svg
                      className="w-4 h-4 text-gray-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 19l-7-7 7-7"
                      />
                    </svg>
                  </button>

                  {/* Right Arrow */}
                  <button
                    className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-md hover:bg-white transition-all duration-200"
                    onClick={() => {
                      const container = document.getElementById(
                        'mobile-tabs-container',
                      );
                      if (container) {
                        container.scrollBy({ left: 200, behavior: 'smooth' });
                      }
                    }}
                  >
                    <svg
                      className="w-4 h-4 text-gray-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </button>

                  {/* Scrollable tabs container */}
                  <div
                    id="mobile-tabs-container"
                    className="overflow-x-auto pb-2 mx-8"
                    style={{
                      scrollbarWidth: 'none',
                      msOverflowStyle: 'none',
                      WebkitOverflowScrolling: 'touch',
                    }}
                  >
                    <TabsList className="flex flex-row h-fit items-stretch justify-start gap-2 !bg-transparent !p-0 !border-none !shadow-none w-max pb-2 px-4">
                      {tabsData.map((tab) => (
                        <motion.div
                          key={tab.id}
                          variants={{
                            hidden: { opacity: 0, x: 20 },
                            visible: {
                              opacity: 1,
                              x: 0,
                              transition: { duration: 0.4, ease: 'easeOut' },
                            },
                          }}
                        >
                          <TabsTrigger
                            value={tab.id}
                            className="justify-center text-center px-4 py-3 text-sm font-normal text-gray-600 hover:text-[#00646C] data-[state=active]:text-[#00646C] data-[state=active]:font-semibold data-[state=active]:!border-b-2 data-[state=active]:!border-[#00646C] !bg-transparent data-[state=active]:!bg-transparent hover:!bg-transparent transition-all duration-300 border-0 !shadow-none !rounded-none min-h-0 h-auto whitespace-nowrap flex-shrink-0"
                            style={{
                              background: 'transparent !important',
                              borderTop: 'none !important',
                              borderRight: 'none !important',
                              borderBottom: 'none !important',
                              boxShadow: 'none !important',
                              borderRadius: '0 !important',
                            }}
                          >
                            {tab.title}
                          </TabsTrigger>
                        </motion.div>
                      ))}
                    </TabsList>
                  </div>
                </div>
              </div>

              {/* Desktop: Vertical tabs */}
              <div className="hidden lg:block">
                <TabsList className="flex flex-col h-fit items-stretch justify-start gap-1 !bg-transparent !p-0 !border-none !shadow-none w-full">
                  {tabsData.map((tab) => (
                    <motion.div
                      key={tab.id}
                      variants={{
                        hidden: { opacity: 0, x: 20 },
                        visible: {
                          opacity: 1,
                          x: 0,
                          transition: { duration: 0.4, ease: 'easeOut' },
                        },
                      }}
                    >
                      <TabsTrigger
                        value={tab.id}
                        className="justify-start text-left px-4 py-4 text-lg xl:text-xl font-normal text-gray-600 hover:text-[#00646C] data-[state=active]:text-[#00646C] data-[state=active]:font-semibold data-[state=active]:!border-l-2 data-[state=active]:!border-l-[#00646C] !bg-transparent data-[state=active]:!bg-transparent hover:!bg-transparent transition-all duration-300 border-0 !shadow-none !rounded-none min-h-0 h-auto hover:scale-105 hover:translate-x-1 whitespace-nowrap flex-shrink-0 w-full"
                        style={{
                          background: 'transparent !important',
                          borderTop: 'none !important',
                          borderRight: 'none !important',
                          borderBottom: 'none !important',
                          boxShadow: 'none !important',
                          borderRadius: '0 !important',
                        }}
                      >
                        {tab.title}
                      </TabsTrigger>
                    </motion.div>
                  ))}
                </TabsList>
              </div>
            </motion.div>
          </motion.div>
        </Tabs>
      </div>
    </motion.section>
  );
}
