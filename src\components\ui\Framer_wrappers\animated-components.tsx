'use client';

import React from 'react';
import { motion } from 'framer-motion';
import type { HTMLMotionProps } from 'framer-motion';

type MotionH1Props = HTMLMotionProps<'h1'>;

const MotionH1 = React.forwardRef<HTMLHeadingElement, MotionH1Props>(
  function MotionH1({ children, ...props }, ref) {
    return (
      <motion.h1 ref={ref} {...props}>
        {children}
      </motion.h1>
    );
  },
);

const MotionH3 = React.forwardRef<HTMLHeadingElement, MotionH1Props>(
  function MotionH3({ children, ...props }, ref) {
    return (
      <motion.h3 ref={ref} {...props}>
        {children}
      </motion.h3>
    );
  },
);

const MotionSpan = React.forwardRef<HTMLSpanElement, MotionH1Props>(
  function MotionSpan({ children, ...props }, ref) {
    return (
      <motion.span ref={ref} {...props}>
        {children}
      </motion.span>
    );
  },
);

const MotionP = React.forwardRef<HTMLParagraphElement, MotionH1Props>(
  function MotionP({ children, ...props }, ref) {
    return (
      <motion.p ref={ref} {...props}>
        {children}
      </motion.p>
    );
  },
);

const MotionDiv = React.forwardRef<HTMLDivElement, MotionH1Props>(
  function MotionDiv({ children, ...props }, ref) {
    return (
      <motion.div ref={ref} {...props}>
        {children}
      </motion.div>
    );
  },
);

const MotionSection = React.forwardRef<HTMLDivElement, MotionH1Props>(
  function MotionSection({ children, ...props }, ref) {
    return (
      <motion.section ref={ref} {...props}>
        {children}
      </motion.section>
    );
  },
);

export { MotionH1, MotionH3, MotionSpan, MotionP, MotionDiv, MotionSection };
