import MiddleContainer from '@/components/middle_container';
import PageBanner from '@/components/page_banner';
import { Metadata, ResolvingMetadata } from 'next';
import { getTranslations } from 'next-intl/server';
import ServicesSection from './components/services_section';
import { headers } from 'next/headers';

export async function generateMetadata(
  { params: { locale } }: { params: { locale: string } },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const t = await getTranslations({
    locale,
    namespace: 'Components.servicesPage',
  });
  return {
    title: t('metaDataTitle'),
    description: t('metaDataDesc'),
    metadataBase: new URL(
      process.env.APP_BASE_URL ?? `https://${headers().get('host')}`,
    ),
  };
}

export default async function Services() {
  const t = await getTranslations('Components.servicesPage');
  const c = await getTranslations('Components.common');

  return (
    <div className="w-full flex flex-col   self-center max-w-[1920px]  pb-24  ">
      <PageBanner
        title={t('bannerTitle')}
        background="linear-gradient(to right, #8e5c7d, #2d4c5c)"
        items={[{ title: t('bannerTitle') }]}
      />
      <MiddleContainer>
        <ServicesSection />
      </MiddleContainer>
    </div>
  );
}
