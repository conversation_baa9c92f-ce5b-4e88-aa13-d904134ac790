import { SitemapRow } from './type';

const mapAlternate = ({ href, hreflang }: { href: string; hreflang: string }) =>
  `<xhtml:link rel="alternate" hreflang="${hreflang}" href="${process.env.APP_BASE_URL + href}"/>`;

const mapRowToUrl = (row: SitemapRow) =>
  `<url>
          <loc>${process.env.APP_BASE_URL + row.url}</loc>
          ${row.lastModified ? `<lastmod>${row.lastModified}</lastmod>` : ''}
          ${row.alternateRefs ? row.alternateRefs.map(mapAlternate).join('\n     ') : ''}
          ${row.changeFrequency ? `<changefreq>${row.changeFrequency}</changefreq>` : ''}
          ${row.priority ? `<priority>${row.priority.toFixed(1)}</priority>` : ''}
      </url>`;

export const mapSite = (
  sitemap: SitemapRow[],
) => `<?xml version="1.0" encoding="UTF-8"?>
  <?xml-stylesheet type="text/xsl" href="/sitemap.xsl"?>
  <urlset 
    xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
    xmlns:xhtml="http://www.w3.org/1999/xhtml"
    xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
      ${sitemap.map(mapRowToUrl).join('\n     ')}
  </urlset>
  `;
