import { ReactNode } from 'react';
import { UrlObject } from 'url';
type Url = string | UrlObject;

export interface ImageDetail {
  url: string;
  xPosition?: 'left' | 'center' | 'right';
  yPosition?: 'top' | 'center' | 'bottom';
  alt?: string;
}

export type MenuItem = {
  id: number;
  name: string;
  href: Url;
  children: MenuItem[];
  target?: string | null;
  image?: string | null;
  description?: string;
  // metaDescription?: string;
  // keywords?: string;
  // permission?: string;
  // level: number;
};

export interface IAppHeader {
  items: MenuItem[];
  logo: ReactNode;
}
