import { ComponentProps, ReactNode } from 'react';
import BreadCrumb from '../bread_crumb';
import { Heading } from '../heading';

interface IAppLayout {
  items: ComponentProps<typeof BreadCrumb>['items'];
  children: ReactNode;
}
export default function AppLayout({ items, children }: IAppLayout) {
  return (
    <div className="flex flex-col self-stretch w-full overflow-hidden  gap-8 ">
      <div className="flex flex-col gap-5 w-full ">
        <Heading title={items[items.length - 1]?.title} />
        <BreadCrumb items={items} />
      </div>
      {children}
    </div>
  );
}
