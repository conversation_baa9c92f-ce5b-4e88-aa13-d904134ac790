'use client';
import TestimonialCard from './testimonial-card';
import { motion } from 'framer-motion';
import { fadeInUp, sectionContainer, cardGrid } from '@/lib/animations';

interface Testimonial {
  id?: string;
  quote: string;
  author: string;
  company: string;
  avatarSrc?: string;
}

interface TestimonialsSectionProps {
  sectionId?: string;
  badge?: string;
  title?: string;
  subtitle?: string;
  testimonials?: Testimonial[];
  className?: string;
}

const defaultTestimonials: Testimonial[] = [
  {
    id: '1',
    quote:
      "We couldn't have done it without their expert setup. The team was professional, efficient, and delivered exactly what we needed for our trade show.",
    author: '<PERSON>',
    company: 'TechVision Inc.',
    avatarSrc:
      '/confident-professional.png?height=40&width=40&query=professional headshot 0',
  },
  {
    id: '2',
    quote:
      'The furniture was high quality and arrived on time. Their attention to detail made our booth stand out from the competition.',
    author: '<PERSON>',
    company: 'Innovate Solutions',
    avatarSrc:
      '/confident-professional.png?height=40&width=40&query=professional headshot 1',
  },
  {
    id: '3',
    quote:
      "Our booth looked better than we imagined. The team's creativity and execution exceeded our expectations. We'll definitely use their services again.",
    author: '<PERSON>',
    company: 'Global Enterprises',
    avatarSrc:
      '/confident-professional.png?height=40&width=40&query=professional headshot 2',
  },
];

export default function TestimonialsSection({
  sectionId = 'testimonials',
  badge = 'Client Feedback',
  title = 'What Our Clients Say',
  subtitle = "Don't just take our word for it. Here's what some of our satisfied clients say about GOODKEY SHOW SERVICES LTD...",
  testimonials = defaultTestimonials,
  className = '',
}: TestimonialsSectionProps) {
  return (
    <motion.section
      id={sectionId}
      className={`w-full py-12 md:py-24 bg-gray-50 ${className}`}
      variants={sectionContainer}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
    >
      <div className="container px-4 md:px-6 overflow-x-hidden">
        <motion.div
          className="text-center max-w-[800px] mx-auto mb-12"
          variants={fadeInUp}
        >
          <div className="inline-block rounded-lg bg-[#00646C]/10 px-3 py-1 text-sm text-[#00646C] font-medium mb-2">
            {badge}
          </div>
          <h2 className="text-3xl font-bold tracking-tighter mb-4">{title}</h2>
          <p className="text-muted-foreground">{subtitle}</p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
          variants={cardGrid}
        >
          {testimonials.map((testimonial, index) => (
            <TestimonialCard
              key={testimonial.id || index}
              quote={testimonial.quote}
              author={testimonial.author}
              company={testimonial.company}
            />
          ))}
        </motion.div>
      </div>
    </motion.section>
  );
}
