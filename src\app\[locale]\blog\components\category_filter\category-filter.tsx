import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { BriefData } from '@/models/BriefData';

interface ICategoryFilter {
  categories: BriefData[];
  handleFilterChange: (value: string) => void;
}

function CategoryFilter({ categories, handleFilterChange }: ICategoryFilter) {
  return (
    <Card className="w-full h-fit   md:max-w-xs px-4 rounded-xl  border-t-[2px] border-t-secondary relative bg-gray-50  ">
      <div className="h-14 w-[2px] bg-secondary absolute top-0 left-[25px]  "></div>
      <CardHeader className="pl-8">
        <CardTitle className="text-2xl font-normal  tracking-wider   border-l-secondary ">
          Categories
        </CardTitle>
      </CardHeader>
      <CardContent className="grid grid-cols-1 tb:grid-cols-2 md:grid-cols-1 xl:grid-cols-1  gap-x-6 gap-y-10 xl:gap-y-6 px-2  pb-10">
        {categories.map((category, index) => (
          <Button
            key={category.id}
            onClick={() => handleFilterChange(category.id.toString())}
            variant="default"
            className={cn(
              'group flex items-center text-foreground/70 hover:text-secondary justify-between pr-3 rounded-none px-0 text-base capitalize transition-all',
              index !== categories.length - 1 &&
                ' xl:border-b xl:border-b-slate-300 xl:pb-8 xl:border-dashed ',
              'md:max-w-[300px]  xl:max-w-[400px] ',
            )}
          >
            <span className="font-medium max-w-[220px] overflow-hidden truncate ">
              {category.name}
            </span>
            <Badge
              variant="destructive"
              className=" rounded-full size-8 text-center flex justify-center items-center  "
            >
              {categories.filter((cat) => cat.id === category.id).length}
            </Badge>
          </Button>
        ))}
      </CardContent>
    </Card>
  );
}

export default CategoryFilter;
