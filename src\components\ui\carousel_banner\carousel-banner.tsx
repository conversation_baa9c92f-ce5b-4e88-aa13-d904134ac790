'use client';
import { Children, ReactNode } from 'react';
import Autoplay from 'embla-carousel-autoplay';
import { cn } from '@/lib/utils';
import Fade from 'embla-carousel-fade';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '../carousel';

interface ICarouselBanner {
  children: ReactNode;
  itemClassName?: string;
  withArrows?: boolean;
  full?: boolean;
}

function CarouselBanner({
  children,
  itemClassName,
  withArrows,
  full = true,
}: ICarouselBanner) {
  const childs = Children.toArray(children);
  return childs.length > 1 ? (
    <Carousel
      plugins={[
        Autoplay({
          delay: 6000,
        }),
        ...(full ? [Fade()] : []),
      ]}
      opts={{
        align: 'start',
        loop: true,
        dragFree: !full,
      }}
      className="w-full group relative "
    >
      <CarouselContent>
        {childs.map((item, index) => (
          <CarouselItem
            key={index}
            className={cn(
              {
                'h-[calc(100vh-120px)]   lg:h-[calc(100vh-180px)] ': full,
              },
              itemClassName,
            )}
          >
            {item}
          </CarouselItem>
        ))}
      </CarouselContent>

      {withArrows && (
        <div className="flex items-center gap-2 absolute bottom-10   left-10 ">
          <CarouselPrevious
            variant={'default'}
            className="group-hover:visible invisible transition-opacity duration-300 rounded-sm ml-2"
          />
          <CarouselNext
            variant={'default'}
            className="group-hover:visible invisible transition-opacity duration-300 rounded-sm mr-2"
          />
        </div>
      )}
    </Carousel>
  ) : (
    <div className="min-h-[60vh] flex">{children}</div>
  );
}

export default CarouselBanner;
