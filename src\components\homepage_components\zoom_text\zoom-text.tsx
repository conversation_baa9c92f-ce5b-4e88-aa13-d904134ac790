'use client';
import './style/index.scss';
import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface IZoomText {
  text: string | ReactNode;
}

const zoomSlideInVariants = {
  initial: { scale: 0.9, fontWeight: 300 },
  animate: {
    scale: [0.9, 0.925, 0.95, 0.975, 1, 0.975, 0.95, 0.925, 0.9],
    fontWeight: [300, 400, 600, 700, 800, 700, 600, 400, 300],
    transition: {
      duration: 4,
      ease: 'linear',
      times: [0, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 1],
      repeat: Infinity,
    },
  },
};

function ZoomText({ text }: IZoomText) {
  return (
    <motion.div
      variants={zoomSlideInVariants}
      initial="initial"
      animate="animate"
      className="text-black text-xl lg:text-[50px] 2xl:text-[90px] bg-gray-200 px-4 py-6 2xl:pb-12 rounded-t-3xl  font-libreFranklin   "
    >
      {text}
    </motion.div>
  );
}

export default ZoomText;
