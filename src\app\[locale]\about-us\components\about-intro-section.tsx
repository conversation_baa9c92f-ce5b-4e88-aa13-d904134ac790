'use client';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { fadeInLeft, fadeInRight, sectionContainer } from '@/lib/animations';

interface AboutIntroSectionProps {
  badge?: string;
  title?: string;
  description?: string;
  paragraphs?: string[];
  imageSrc?: string;
  imageAlt?: string;
  className?: string;
}

const defaultContent = {
  badge: 'About Us',
  title:
    "Goodkey Show Services Ltd. is one of Western Canada's most experienced full-service display contractors.",
  description:
    'We have the capability, equipment, knowledge and expertise to meet any and all of your special event and trade and consumer exhibit requirements.',
  paragraphs: [
    "Difficult or demanding venues are our specialty. No show is too big or small. Whether it's a five hundred booth trade show, we deliver on budget, on time, every time. We're a set-up-the sleeves kind of operation that's inspired by our owner-operator philosophy.",
    'With more than fourteen years of experience, <PERSON> has earned a reputation for the highest standards in excellence and dedication to customer satisfaction.',
    "Our expertise runs the gamut from total design and component manufacturing to fabrication and complete installation of custom exhibits. We have a comprehensive show service inventory that includes an extensive selection of carpeting, hardwall and drapery, booth equipment, lighting and electric signage. And we're always expanding and updating our inventory to anticipate new trends and ever growing demands.",
    "From Toronto to Alaska, we've proven ourselves to be reputable, reliable and professional providers of creative and innovative service to many of the largest shows in North America.",
  ],
  imageSrc: '/about-us/section-1-image.jpg',
  imageAlt: 'Goodkey Show Services trade show setup',
};

export default function AboutIntroSection({
  badge = defaultContent.badge,
  title = defaultContent.title,
  description = defaultContent.description,
  paragraphs = defaultContent.paragraphs,
  imageSrc = defaultContent.imageSrc,
  imageAlt = defaultContent.imageAlt,
  className = '',
}: AboutIntroSectionProps) {
  return (
    <motion.section
      className={`py-8 sm:py-12 md:py-16 lg:py-20 xl:py-24 bg-white ${className}`}
      variants={sectionContainer}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
    >
      <div className="container mx-auto  ">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-12 lg:gap-16 items-start">
          {/* Left Content */}
          <motion.div className="space-y-6" variants={fadeInLeft}>
            {/* Badge */}
            <div className="inline-block">
              <span className="bg-[#00646C]/10 text-[#00646C] px-4 py-2 rounded-full text-sm font-medium">
                {badge}
              </span>
            </div>

            {/* Title */}
            <h2 className="text-[#0A0A0A] text-2xl md:text-3xl lg:text-4xl font-bold  leading-tight">
              {title}
            </h2>

            {/* Content Paragraphs */}
            <div className="space-y-4">
              <p className="text-[#0A0A0A] leading-relaxed font-medium text-base lg:text-xl py-8">
                {description}
              </p>
              {paragraphs.map((paragraph, index) => (
                <p
                  key={index}
                  className="text-[#0A0A0A] leading-relaxed text-base "
                >
                  {paragraph}
                </p>
              ))}
            </div>
          </motion.div>

          {/* Right Image */}
          <motion.div className="relative w-full" variants={fadeInRight}>
            <div className="relative w-full aspect-[4/3] sm:aspect-[3/2] md:aspect-[4/3] lg:aspect-[5/4] xl:aspect-[4/3] rounded-lg overflow-hidden shadow-lg">
              <Image
                src={imageSrc}
                alt={imageAlt}
                fill
                className="object-cover object-center"
                sizes="(max-width: 640px) 100vw, (max-width: 768px) 90vw, (max-width: 1024px) 45vw, (max-width: 1280px) 40vw, 600px"
                priority
              />
            </div>

            {/* Decorative element - responsive sizing */}
            <div className="absolute -bottom-2 -right-2 sm:-bottom-3 sm:-right-3 md:-bottom-4 md:-right-4 w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 bg-[#00646C]/10 rounded-lg -z-10"></div>
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
}
