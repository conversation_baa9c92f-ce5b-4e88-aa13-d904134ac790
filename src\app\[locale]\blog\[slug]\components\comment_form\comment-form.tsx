'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Form, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { Check, X } from 'lucide-react';
import { ReloadIcon } from '@radix-ui/react-icons';
import { cn } from '@/lib/utils';

import './style/index.scss';
import { commentSchema } from '@/schemas/comment';
import {
  FloatingLabelInput,
  FloatingLabelTextarea,
} from '@/components/ui/inputs/floating-label-input';
interface ICommentForm {}

function CommentForm({}: ICommentForm) {
  const c = useTranslations('Components.common');
  const t = useTranslations('Components.Contact');
  const [loading, setLoading] = useState(false);
  const [isSuccess, setISSuccess] = useState(false);
  const [isError, setError] = useState(false);

  const form = useForm<z.infer<typeof commentSchema>>({
    resolver: zodResolver(commentSchema),
    defaultValues: {
      name: '',
      email: '',
      message: '',
    },
  });

  async function onSubmit(data: z.infer<typeof commentSchema>) {
    setLoading(true);
    // console.log(data);

    form.reset();
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex w-full flex-col gap-12 rounded-lg bg-card px-8  md:max-w-[700px] md:px-14 pt-12 pb-16     shadow-lg font-devanagari "
      >
        <h2 className="text-3xl font-semibold text-primary">
          {t('leaveComment')}
        </h2>
        <div className="grid grid-cols-1 gap-10">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FloatingLabelInput {...field} id="name" label={t('name')} />
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FloatingLabelInput {...field} id="email" label={t('email')} />
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="message"
            render={({ field }) => (
              <FormItem>
                <FloatingLabelTextarea
                  {...field}
                  id="message"
                  label={t('message')}
                />
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="flex items-center ">
          <Button
            variant={isError ? 'destructive' : 'default'}
            className={cn(
              'min-w-[150px] h-12 text-base rounded-lg duration-500 hover:scale-105 hover:bg-primary',
            )}
          >
            {loading ? (
              <ReloadIcon className="mr-2 h-6 w-6 animate-spin" />
            ) : isSuccess ? (
              <>
                <Check className="mr-2 h-6 w-6" />
                {c('success')}
              </>
            ) : isError ? (
              <>
                <X className="mr-2 h-6 w-6" />
                {c('error')}
              </>
            ) : (
              t('submit')
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default CommentForm;
