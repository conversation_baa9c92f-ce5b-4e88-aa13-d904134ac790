'use client';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import RequestQuery from '@/services/queries/contact';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { Send } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

interface ISubscribeForm {}
const schema = z.object({
  email: z.string().email(),
});
function SubscribeForm({}: ISubscribeForm) {
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: { email: '' },
  });
  const { toast } = useToast();

  const { mutate, isPending } = useMutation({
    mutationFn: RequestQuery.subscribe,
    mutationKey: ['subscribe'],
    onSuccess: () => {
      form.reset();
      toast({
        title: 'Submitted',
        description: 'Your email has been submitted',
        variant: 'success',
      });
    },
    onError: () => {
      toast({
        title: 'Oops',
        description: 'Already subscribed',
        variant: 'default',
      });
    },
  });
  const t = useTranslations('Components.navigation');
  return (
    <div className="self-stretch flex-col justify-start items-stretch gap-8 flex    relative ">
      <div className="flex flex-col gap-3 w-full ">
        <span className="text-white text-xl font-semibold ">
          {t('subscribe')}
        </span>
        <span className="text-white text-sm">{t('stayUpToDate')}</span>
      </div>
      <Form {...form}>
        <form
          className="flex items-end"
          onSubmit={form.handleSubmit((data) => {
            mutate(data.email);
          })}
        >
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormControl>
                  <Input
                    placeholder={t('enterYourEmail')}
                    {...field}
                    className="rounded-none h-[55px] pr-[80px] flex-grow-1 bg-transparent border border-white placeholder:text-white focus-visible:border-secondary  text-white"
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <Button
            type="submit"
            disabled={isPending}
            className="h-[55px] w-[55px] bg-gray-600 hover:bg-gray-700  absolute right-0  rounded-none  "
          >
            <Send />
          </Button>
        </form>
      </Form>
    </div>
  );
}

export default SubscribeForm;
