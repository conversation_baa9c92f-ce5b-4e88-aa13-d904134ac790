'use client';
import { motion } from 'framer-motion';
interface ITransition {
  children?: React.ReactNode;
}

function Template({ children }: ITransition) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{
        duration: 1,
        ease: 'easeInOut',
      }}
      className="flex w-full  self-center  flex-col items-center "
    >
      {children}
    </motion.div>
  );
}

export default Template;
