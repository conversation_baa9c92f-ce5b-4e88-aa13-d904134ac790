import { cn } from '@/lib/utils';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '../card';
import { ReactNode } from 'react';

interface ICardGenerator {
  title?: string | ReactNode;
  description?: string | ReactNode;
  children?: React.ReactNode;
  footer?: React.ReactNode;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  descriptionClassName?: string;
  onClick?: () => void;
}

function CardGenerator({
  title,
  children,
  description,
  footer,
  className,
  onClick,
  headerClassName,
  descriptionClassName,
  contentClassName,
}: ICardGenerator) {
  return (
    <Card className={cn('w-full  ', className)} onClick={onClick}>
      {(title || description) && (
        <CardHeader className={cn(headerClassName)}>
          {title ? (
            typeof title === 'string' ? (
              <CardTitle>{title}</CardTitle>
            ) : (
              title
            )
          ) : null}

          {description ? (
            typeof description === 'string' ? (
              <CardDescription className={cn(descriptionClassName)}>
                {description}
              </CardDescription>
            ) : (
              description
            )
          ) : null}
        </CardHeader>
      )}
      {children && (
        <CardContent className={cn(contentClassName)}>{children}</CardContent>
      )}
      {footer && <CardFooter>{footer}</CardFooter>}
    </Card>
  );
}

export default CardGenerator;
