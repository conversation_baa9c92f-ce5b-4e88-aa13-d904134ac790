'use client';
import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { FaFacebook, FaLinkedin } from 'react-icons/fa6';

interface ISocialMediaLinks {}

function SocialMediaLinks({}: ISocialMediaLinks) {
  return (
    <div className="w-full px-5  flex flex-row gap-1 flex-wrap ">
      <Link
        rel="noopener noreferrer"
        target="_blank"
        href={`https://www.facebook.com/sharer/sharer.php?u=${global?.window?.location.href as string}`}
      >
        <Button variant="default" className="group px-2">
          <FaFacebook className="w-6 h-6 text-black group-hover:text-primary " />
        </Button>
      </Link>
      {/* <Link rel="noopener noreferrer"    target="_blank" href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`}>
  <Button variant="transparent" className="group px-2">
    <FaInstagram className="w-6 h-6 text-black group-hover:text-primary" />
  </Button>
  </Link> */}
      <Link
        rel="noopener noreferrer"
        target="_blank"
        href={`https://www.linkedin.com/shareArticle?mini=true&url=${global?.window?.location.href as string}`}
      >
        <Button variant="default" className="group px-2">
          <FaLinkedin className="w-6 h-6 text-black group-hover:text-primary" />
        </Button>
      </Link>
    </div>
  );
}

export default SocialMediaLinks;
