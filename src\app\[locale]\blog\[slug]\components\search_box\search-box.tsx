'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { SearchIcon } from 'lucide-react';

interface SearchBoxProps {
  initialQuery?: string;
}

function SearchBox({ initialQuery = '' }: SearchBoxProps) {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState(initialQuery);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    // Trim and validate search term
    const trimmedQuery = searchTerm.trim();

    // Navigate to search results page with query
    const encodedQuery = encodeURIComponent(trimmedQuery);
    router.push(`/blog?search=${encodedQuery}`);
  };

  return (
    <div className="w-full h-fit self-start xl:self-center max-w-xs p-6 rounded-xl  border-t-[2px] border-t-secondary relative bg-gray-50 ">
      <div className="h-14 w-[2px] bg-secondary absolute top-0 left-[25px]  "></div>

      <h2 className="text-2xl font-normal  tracking-wider   border-l-secondary mb-4 pl-8">
        Search
      </h2>

      <form className="flex flex-row items-center" onSubmit={handleSearch}>
        <input
          type="search"
          placeholder="Search..."
          className="flex-1 h-10 px-3 py-2 bg-white rounded-md rounded-r-none focus:outline-none  focus:border-transparent"
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <Button
          type="submit"
          size="icon"
          className="bg-[#1a2b4c] w-[40px]  rounded-r-md rounded-l-none h-10 hover:bg-[#243a66] disabled:opacity-50"
        >
          <SearchIcon className="h-4 w-4" />
          <span className="sr-only">Search</span>
        </Button>
      </form>
    </div>
  );
}

export default SearchBox;
