export function normalizeString(input: string): string {
  let normalized = input
    .replace(/ /g, '-')
    .replace(/\//g, '')
    .replace(/&/g, 'and');

  // Convert to lowercase
  normalized = normalized.toLowerCase();

  return normalized;
}

export function convertToUrlString(text: string): string {
  // Remove non-alphanumeric characters and replace with hyphen
  const replacements: { [key: string]: string } = {
    à: 'a',
    â: 'a',
    ä: 'a',
    á: 'a',
    ã: 'a',
    è: 'e',
    é: 'e',
    ê: 'e',
    ë: 'e',
    ì: 'i',
    í: 'i',
    î: 'i',
    ï: 'i',
    ò: 'o',
    ó: 'o',
    ô: 'o',
    ö: 'o',
    õ: 'o',
    ø: 'o',
    ù: 'u',
    ú: 'u',
    û: 'u',
    ü: 'u',
    ÿ: 'y',
    ç: 'c',
  };

  let urlString = text
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, (char) => {
      return replacements[char] || '';
    })
    .replace(/\s/g, '-');

  // Remove consecutive hyphens
  urlString = urlString.replace(/-+/g, '-');

  return urlString;
}
