import { ReactNode } from 'react';
import './style/index.scss';
interface IVideoBanner {
  videoSrc: string;
  fallbackImageSrc?: string;
  description?: string | ReactNode;
}

function VideoBanner({
  videoSrc,
  fallbackImageSrc,
  description,
}: IVideoBanner) {
  return (
    <div className="relative w-full   overflow-hidden">
      <video
        className=" w-full h-[400px] object-cover lg:h-full    lg:object-contain   "
        autoPlay
        loop
        muted
        playsInline
      >
        <source src={videoSrc} type="video/mp4" />
      </video>
      {fallbackImageSrc && (
        <img
          src={fallbackImageSrc}
          alt="Video Banner Fallback"
          className="absolute top-0 left-0 w-full h-full object-cover"
        />
      )}
      {description ? (
        typeof description === 'string' ? (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <h1 className="text-white text-4xl md:text-6xl font-bold text-center">
              {description}
            </h1>
          </div>
        ) : (
          description
        )
      ) : null}
    </div>
  );
}

export default VideoBanner;
