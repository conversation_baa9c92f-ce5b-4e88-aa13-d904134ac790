'use client';
import { useEffect, useState } from 'react';
import CategoryTabs from './CategoryTabs';
import GallerySubcategorySection from './GallerySubcategorySection';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import galleryQuery from '@/services/queries/galleryQuery';
import {
  GalleryCategory,
  GallerySubcategory,
  GalleryImage,
} from '@/models/GalleryModels';
import { cn } from '@/lib/utils';

export default function GalleryClient() {
  const [categories, setCategories] = useState<GalleryCategory[] | null>(null);
  const [subcategoriesByCategory, setSubcategoriesByCategory] = useState<
    Record<number, GallerySubcategory[]>
  >({});
  const [imagesBySubcategory, setImagesBySubcategory] = useState<
    Record<number, GalleryImage[]>
  >({});
  const [activeCategoryId, setActiveCategoryId] = useState<number | undefined>(
    undefined,
  );
  const [activeSubcategoryId, setActiveSubcategoryId] = useState<
    number | undefined
  >(undefined);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      const cats = await galleryQuery.getCategories();
      setCategories(cats);
      const firstCatId = cats[0]?.id;
      setActiveCategoryId(firstCatId);
      const subcatsByCat: Record<number, GallerySubcategory[]> = {};
      const imgsBySubcat: Record<number, GalleryImage[]> = {};
      for (const cat of cats) {
        const subcats = await galleryQuery.getSubcategoriesByCategory(cat.id);
        subcatsByCat[cat.id] = subcats;
        for (const subcat of subcats) {
          imgsBySubcat[subcat.id] = await galleryQuery.getImagesBySubcategory(
            subcat.id,
          );
        }
      }
      setSubcategoriesByCategory(subcatsByCat);
      setImagesBySubcategory(imgsBySubcat);
      // Set first subcategory as active
      if (firstCatId && subcatsByCat[firstCatId]?.length) {
        setActiveSubcategoryId(subcatsByCat[firstCatId][0].id);
      }
      setLoading(false);
    }
    fetchData();
  }, []);

  // When category changes, update active subcategory
  useEffect(() => {
    if (activeCategoryId && subcategoriesByCategory[activeCategoryId]?.length) {
      setActiveSubcategoryId(subcategoriesByCategory[activeCategoryId][0].id);
    }
  }, [activeCategoryId, subcategoriesByCategory]);

  if (loading || !categories) {
    return (
      <div className="flex flex-col gap-10 items-start container mx-auto">
        <div className="w-full h-[220px] bg-[#00646C] rounded-b-2xl flex items-end px-8 py-6">
          <Skeleton className="h-12 w-1/2" />
        </div>
        <div className="flex flex-row gap-10 w-full mt-8">
          <div className="flex flex-col gap-2 min-w-[260px]">
            {[...Array(3)].map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
          <div className="flex-1 flex flex-col gap-12">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="mb-12">
                <Skeleton className="h-8 w-1/3 mb-2" />
                <Skeleton className="h-5 w-1/2 mb-4" />
                <Skeleton className="h-80 w-full" />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const activeCategory = categories.find((cat) => cat.id === activeCategoryId);
  const subcategories = activeCategoryId
    ? subcategoriesByCategory[activeCategoryId] || []
    : [];
  const activeSubcategory = subcategories.find(
    (sub) => sub.id === activeSubcategoryId,
  );

  return (
    <div className="w-full flex flex-col items-center gap-10 ">
      {/* Top section with background and category tabs */}
      <div
        className="w-full flex items-center  min-h-[400px] "
        style={{ backgroundColor: 'rgba(0, 100, 108, 0.75)' }}
      >
        <div className="w-full container mx-auto pt-10 pb-8 flex flex-col items-start  justify-center ">
          <div className="text-white/90 text-base mb-6 ml-12  ">
            Please click on one of the galleries below to view photos.
          </div>
          <div className="w-full flex flex-row gap-8">
            <Tabs
              value={activeCategoryId?.toString() || ''}
              onValueChange={(val) => setActiveCategoryId(Number(val))}
            >
              <CategoryTabs
                categories={categories}
                value={activeCategoryId?.toString() || ''}
                onValueChange={(val) => setActiveCategoryId(Number(val))}
              />
            </Tabs>
          </div>
        </div>
      </div>
      {/* Category description section */}
      {activeCategory && (
        <div className="w-full container mx-auto ">
          <div className="text-base text-muted-foreground whitespace-pre-line">
            {activeCategory.description}
          </div>
        </div>
      )}
      {/* Main content: vertical subcategory tabs and content */}
      <div className="w-full container mx-auto flex flex-col md:flex-row gap-4 mt-12 ">
        {/* Vertical subcategory tabs */}
        <Tabs
          value={activeSubcategoryId?.toString()}
          onValueChange={(val) => setActiveSubcategoryId(Number(val))}
          orientation="vertical"
          className="min-w-[260px]  md:w-1/4 "
        >
          <TabsList className="flex flex-col gap-2 bg-white/80 rounded-lg pb-2 w-full h-full justify-start  ">
            {subcategories.map((subcat, index) => (
              <TabsTrigger
                key={subcat.id}
                value={subcat.id.toString()}
                className={cn(
                  'justify-start whitespace-normal ml-0 pl-4 text-left w-full pr-0 py-2 border-l-4 border-transparent data-[state=active]:border-[#197D80] data-[state=active]:text-[#197D80] data-[state=active]:font-bold data-[state=active]:text-xl data-[state=inactive]:text-muted-foreground data-[state=inactive]:font-normal data-[state=inactive]:text-lg transition-all',
                  index == 0 && 'pt-0',
                )}
              >
                {subcat.name}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
        {/* Right panel: subcategory content */}
        <div className="flex-1  md:w-3/4">
          {activeSubcategory && (
            <GallerySubcategorySection
              subcategory={activeSubcategory}
              images={imagesBySubcategory[activeSubcategory.id] || []}
            />
          )}
        </div>
      </div>
    </div>
  );
}
