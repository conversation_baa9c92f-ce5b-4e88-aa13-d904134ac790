import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

interface IBannerBox {
  title: string;
  href: string;
  className?: string;
}

function BannerBox({ className, title, href }: IBannerBox) {
  const c = useTranslations('Components.common');
  return (
    <div
      className={cn(
        'z-20  banner-box    flex flex-col gap-5 sm:gap-6 text-left',
        'w-[90%] sm:w-[80%] lg:max-w-[500px] ',
        'bg-black/30 p-10 rounded-xl border border-gray-500 shadow-lg',
        'absolute  top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 ',
        ' lg:left-[10%] lg:translate-x-0',
        className,
      )}
    >
      <h2 className="text-white text-2xl sm:text-4xl font-medium   ">
        {title}
      </h2>
      <Link href={href} className="w-fit">
        <Button
          variant={'secondary'}
          className="rounded-sm h-11 md:h-12 px-6 uppercase "
        >
          {c('learnMore')}
        </Button>
      </Link>
    </div>
  );
}

export default BannerBox;
