import axios from 'axios';
import { AppError } from './error';

export default async function fetcher<T>(
  url: string | URL | Request,
  init?: RequestInit | undefined,
  noProxy: boolean = false,
) {
  const fullUrl =
    typeof window !== 'undefined' && !noProxy
      ? `/api/${url}`
      : `${process.env.API_BASE_URL ?? process.env.NEXT_PUBLIC_API_BASE_URL}/api/${url}`;
  const response = await fetch(fullUrl, {
    ...init,
  });

  if (!response.ok) {
    throw new AppError(
      `HTTP error! Status: ${response.status}`,
      response.status,
    );
  }

  const data = (await response.json()) as ApiResponse<T>;

  if (data.statusCode !== 200) {
    throw new AppError(
      data.message ?? 'Something went wrong',
      data.statusCode ?? response.status,
    );
  }
  return data.data;
}
export async function proxyFetch<T>(
  url: string | URL | Request,
  init?: RequestInit | undefined,
) {
  const response = await fetch(`api/${url}`, init);

  if (!response.ok) {
    throw new AppError(
      `HTTP error! Status: ${response.status}`,
      response.status,
    );
  }

  const data = (await response.json()) as ApiResponse<T>;
  if (data.statusCode !== 200) {
    throw new AppError(
      `HTTP error! Status: ${response.status}`,
      response.status,
    );
  }
  return data.data;
}
const axiosInstance = axios.create();

axiosInstance.interceptors.request.use((config) => {
  config.url = `${process.env.API_BASE_URL}/api/${config.url}`;
  return config;
});
export { axiosInstance };
