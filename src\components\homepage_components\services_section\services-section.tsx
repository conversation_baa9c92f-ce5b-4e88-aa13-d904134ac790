'use client';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';
import ServiceCard from './service-card';
import { motion } from 'framer-motion';
import {
  fadeInUp,
  sectionContainer,
  cardGrid,
  staggerContainer,
  fadeInRight,
  fadeInLeft,
} from '@/lib/animations';

interface Service {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface ServicesSectionProps {
  sectionId?: string;
  title?: string;
  subtitle?: string;
  services?: Service[];
  expertSectionTitle?: string;
  expertSectionDescription?: string;
  expertSectionImage?: string;
  expertSectionImageAlt?: string;
  features?: string[];
  ctaButtonText?: string;
  ctaButtonHref?: string;
  className?: string;
}

// Default service icons
const defaultIcons = {
  installation: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect width="18" height="18" x="3" y="3" rx="2" />
      <path d="M3 9h18" />
      <path d="M9 21V9" />
    </svg>
  ),
  signage: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z" />
      <path d="M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8" />
      <path d="M12 17.5v.5" />
      <path d="M12 6v.5" />
    </svg>
  ),
  display: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M2 7h18m-18 0v11a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V7M2 7V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v2" />
      <path d="M6 12a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2H8a2 2 0 0 1-2-2z" />
    </svg>
  ),
  furniture: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M20 10V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v3" />
      <path d="M20 14H4" />
      <path d="M4 10v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V10" />
      <path d="M12 10v10" />
    </svg>
  ),
  planning: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
      <line x1="16" x2="16" y1="2" y2="6" />
      <line x1="8" x2="8" y1="2" y2="6" />
      <line x1="3" x2="21" y1="10" y2="10" />
      <path d="m9 16 2 2 4-4" />
    </svg>
  ),
  collection: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M3 9a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
      <path d="M5 13h14" />
      <path d="M9 17v2" />
      <path d="M15 17v2" />
      <path d="M5 7V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v2" />
    </svg>
  ),
};

const defaultServices: Service[] = [
  {
    id: 'installation',
    title: 'Trade Show Installation',
    description:
      'Professional installation services for trade show booths and exhibits of all sizes.',
    icon: defaultIcons.installation,
  },
  {
    id: 'signage',
    title: 'Trade Show Signage',
    description:
      'Custom signage solutions including banners, displays, and digital signage for maximum visibility.',
    icon: defaultIcons.signage,
  },
  {
    id: 'display',
    title: 'Trade Show Display',
    description:
      'Eye-catching display solutions designed to attract attention and showcase your brand.',
    icon: defaultIcons.display,
  },
  {
    id: 'furniture',
    title: 'Rental Furnishings',
    description:
      'High-quality furniture rentals including seating, tables, and accessories for your event.',
    icon: defaultIcons.furniture,
  },
  {
    id: 'planning',
    title: 'Event Planning',
    description:
      'Comprehensive event planning services from concept to execution for successful events.',
    icon: defaultIcons.planning,
  },
  {
    id: 'collection',
    title: 'Optimum Furniture Collection',
    description:
      'Sophisticated furniture collection to elevate the look and feel of your event or exhibit.',
    icon: defaultIcons.collection,
  },
];

const defaultFeatures = [
  'Custom booth design and fabrication',
  'Professional installation and dismantling',
  'High-quality graphics and signage',
  'Premium furniture and accessory rentals',
];

export default function ServicesSection({
  sectionId = 'services',
  title = 'Our Services',
  subtitle = 'We offer a range of professional event management services to meet your needs.',
  services = defaultServices,
  expertSectionTitle = 'Expert Trade Show Solutions',
  expertSectionDescription = 'At Goodkey, we specialize in creating stunning trade show booths that capture attention and effectively communicate your brand message. From design to installation, we handle every detail to ensure your exhibition success.',
  expertSectionImage = '/homepage/services/service-section-image.jpg',
  expertSectionImageAlt = 'GOODKEY SHOW SERVICES LTD. Professional Trade Show Booth',
  features = defaultFeatures,
  ctaButtonText = 'Get a Quote',
  ctaButtonHref = '#',
  className = '',
}: ServicesSectionProps) {
  return (
    <motion.section
      id={sectionId}
      className={`w-full ${className}`}
      variants={sectionContainer}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
    >
      <div className="relative py-12 md:py-24 overflow-hidden">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: 'url(/homepage/services-section-background.jpg)',
          }}
        />

        {/* Overlay */}
        <div
          className="absolute inset-0"
          style={{
            background: 'rgba(0, 91, 99, 0.95)',
          }}
        />

        {/* Content */}
        <div className="container px-4 md:px-6 overflow-x-hidden relative z-20">
          <motion.div
            className="text-center max-w-[800px] mx-auto mb-12"
            variants={fadeInUp}
          >
            <h2 className="text-3xl font-bold tracking-tighter mb-4 text-white">
              {title}
            </h2>
            <p className="text-white/90">{subtitle}</p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-6"
            variants={cardGrid}
          >
            {services.map((service) => (
              <ServiceCard
                key={service.id}
                icon={service.icon}
                title={service.title}
                description={service.description}
              />
            ))}
          </motion.div>
        </div>
      </div>
      <motion.div
        variants={staggerContainer}
        className="container px-4 md:px-6"
      >
        <div className="py-16 grid md:grid-cols-2 gap-8 items-center bg-white">
          <motion.div
            className="relative h-[400px] rounded-lg overflow-hidden  "
            variants={fadeInLeft}
          >
            <Image
              src={expertSectionImage}
              alt={expertSectionImageAlt}
              fill
              className="object-cover"
              unoptimized
            />
          </motion.div>
          <motion.div className="text-[#0A0A0A]" variants={fadeInRight}>
            <h3 className="text-2xl font-bold mb-6">{expertSectionTitle}</h3>
            <p className="mb-6">{expertSectionDescription}</p>
            <ul className="space-y-4">
              {features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#0A0A0A] mr-2 mt-0.5" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
            <Button className="mt-8 bg-[#004B4B] hover:bg-[#004B4B]/90 text-white font-medium">
              {ctaButtonText}
            </Button>
          </motion.div>
        </div>
      </motion.div>
    </motion.section>
  );
}
