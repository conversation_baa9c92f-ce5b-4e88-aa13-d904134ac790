interface TestimonialCardProps {
  quote: string;
  author: string;
  company: string;
  className?: string;
}

// Quote icon component
const QuoteIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    className="h-10 w-10 text-[#00646C]/20 mb-4"
  >
    <path
      d="M10 11h-4a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1z"
      fill="currentColor"
    />
    <path
      d="M18 11h-4a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1z"
      fill="currentColor"
    />
    <path
      d="M10 19h-4a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1z"
      fill="currentColor"
    />
    <path
      d="M18 19h-4a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1z"
      fill="currentColor"
    />
  </svg>
);

export default function TestimonialCard({
  quote,
  author,
  company,
  className = '',
}: TestimonialCardProps) {
  // Function to get initials from first and last name
  const getInitials = (name: string): string => {
    const names = name.trim().split(' ');
    if (names.length === 1) {
      return names[0].charAt(0).toUpperCase();
    }
    const firstName = names[0];
    const lastName = names[names.length - 1];
    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
  };

  return (
    <div
      className={`bg-white p-6 rounded-lg shadow-sm border border-gray-100 ${className}`}
    >
      <QuoteIcon />
      <p className="text-muted-foreground mb-4">"{quote}"</p>
      <div className="flex items-center">
        <div className="h-10 w-10 rounded-full bg-[#00646C] mr-3 flex items-center justify-center">
          <span className="text-white text-sm font-semibold">
            {getInitials(author)}
          </span>
        </div>
        <div>
          <p className="font-medium">{author}</p>
          <p className="text-sm text-muted-foreground">{company}</p>
        </div>
      </div>
    </div>
  );
}
