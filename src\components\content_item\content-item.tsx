import CardGenerator from '@/components/ui/card_generator';
import { cn } from '@/lib/utils';
import { ReactNode } from 'react';

interface IContentItem {
  title: string | ReactNode;
  description: string;
  children?: ReactNode;
  footer?: ReactNode;
  headerClassName?: string;
  className?: string;
  descriptionClassName?: string;
  contentClassName?: string;
}

function ContentItem({
  title,
  description,
  children,
  footer,
  headerClassName,
  className,
  descriptionClassName,
  contentClassName,
}: IContentItem) {
  return (
    <div
      // style={{
      //   backgroundImage: `url(${backgroundImage})`,
      //   backgroundRepeat: 'no-repeat',
      //   backgroundSize: 'cover',
      //   backgroundPosition: 'center',
      // }}
      className={cn('w-full flex items-center  justify-center relative  ')}
    >
      <div
        className="absolute top-0 left-0 right-0 bottom-0   z-[2] "
        // style={{
        //   background: 'rgba(0, 82, 136, 0.64)',
        // }}
      ></div>
      <CardGenerator
        className={cn(
          'shadow-none border-none px-10 rounded-sm gap-10 h-[400x] w-fit z-[3]   ',
          '',
          className,
        )}
        contentClassName={cn(' flex flex-col justify-end   ', contentClassName)}
        headerClassName={cn('gap-3 p-0  ', headerClassName)}
        descriptionClassName={cn(
          'font-devanagari font-normal text-white leading-[25px] xl:leading-[35px] text-2xl max-w-[750px] ',
          descriptionClassName,
        )}
        title={title}
        description={description}
        footer={footer}
      >
        {children}
      </CardGenerator>
    </div>
  );
}

export default ContentItem;
