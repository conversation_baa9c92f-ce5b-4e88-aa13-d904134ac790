'use client';
import { useQuery } from '@tanstack/react-query';
import articlesQuery from '@/services/queries/ArticleQuery';
import Suspense from '@/components/ui/Suspense';
import { useParams } from 'next/navigation';
import RecentPanel from '../../../components/recent_panel';
import SearchBox from '../search_box';

interface IArticleSideBar {}

function ArticleSideBar({}: IArticleSideBar) {
  const { slug } = useParams();
  const { data: articles, isLoading: isLoadingArticles } = useQuery({
    queryKey: [...articlesQuery.tags],
    queryFn: articlesQuery.getBrief,
  });

  const recentArticles =
    articles
      ?.filter(
        (article) =>
          typeof slug === 'string' &&
          article.slug !== slug.replace(/%20/g, ' '),
      )
      .sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      )
      .slice(0, 3) || [];

  return (
    <Suspense isLoading={isLoadingArticles}>
      <aside className="article-side-bar flex flex-col gap-10 items-center w-full xl:min-w-[350px] xl:max-w-[350px] ">
        <SearchBox />
        <RecentPanel articles={recentArticles} />
      </aside>
    </Suspense>
  );
}

export default ArticleSideBar;
