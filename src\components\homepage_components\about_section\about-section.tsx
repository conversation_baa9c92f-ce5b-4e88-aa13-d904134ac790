'use client';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { MapPin, CheckCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { fadeInLeft, fadeInRight, sectionContainer } from '@/lib/animations';

interface AboutSectionProps {
  sectionId?: string;
  badge?: string;
  title?: string;
  description?: string;
  whatWeDoTitle?: string;
  whatWeDoDescription?: string;
  readMoreHref?: string;
  orderNowHref?: string;
  readMoreText?: string;
  orderNowText?: string;
  className?: string;
}

export default function AboutSection({
  sectionId = 'about',
  badge = 'Who We Are',
  title = 'About GOODKEY SHOW SERVICES LTD.',
  description = 'We are a full-service event management company based in Western Canada. We specialize in providing services for trade shows, exhibits, and conventions, with a focus on quality and customer satisfaction.',
  whatWeDoTitle = 'What We Do',
  whatWeDoDescription = 'We deliver customized event solutions including trade show installation, rentals, planning, and more. Our experienced team ensures seamless execution and visually impactful displays.',
  readMoreHref = '#',
  orderNowHref = 'https://admingoodkey.malopan.com/login',
  readMoreText = 'Read More',
  orderNowText = 'Order Now',
  className = '',
}: AboutSectionProps) {
  return (
    <motion.section
      id={sectionId}
      className={`w-full py-12 md:py-24 bg-gray-50 ${className}`}
      variants={sectionContainer}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
    >
      <div className="container px-4 md:px-6 overflow-x-hidden">
        <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
          <motion.div className="space-y-4" variants={fadeInLeft}>
            <div className="inline-block rounded-lg bg-[#00646C]/10 px-3 py-1 text-sm text-[#00646C] font-medium mb-2">
              {badge}
            </div>
            <h2 className="text-3xl font-bold tracking-tighter">{title}</h2>
            <p className="text-muted-foreground">{description}</p>
            <div className="space-y-2">
              <h3 className="text-xl font-semibold">{whatWeDoTitle}</h3>
              <p className="text-muted-foreground">{whatWeDoDescription}</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 pt-2">
              <Button
                variant="outline"
                className="border-[#00646C] text-[#00646C] hover:bg-[#00646C]/10"
              >
                {readMoreText}
              </Button>
              <Link
                href={orderNowHref}
                target="_blank"
                rel="noopener noreferrer"
              >
                <Button className="bg-[#B10055] hover:bg-[#B10055]/90 text-white">
                  {orderNowText}
                </Button>
              </Link>
            </div>
          </motion.div>
          <motion.div className="grid grid-cols-2 gap-4" variants={fadeInRight}>
            <div className="bg-white p-6 rounded-lg shadow-sm border border-card-border flex flex-col items-center text-center">
              <div className="h-12 w-12 rounded-full bg-[#00646C]/10 flex items-center justify-center mb-4">
                <MapPin className="h-6 w-6 text-[#00646C]" />
              </div>
              <h3 className="text-lg font-medium mb-2">
                Serving Western Canada
              </h3>
              <p className="text-sm text-muted-foreground">
                From Edmonton to Vancouver, we bring trade show services to
                cities across Western Canada.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm border border-card-border flex flex-col items-center text-center">
              <div className="h-12 w-12 rounded-full bg-[#00646C]/10 flex items-center justify-center mb-4">
                <CheckCircle className="h-6 w-6 text-[#00646C]" />
              </div>
              <h3 className="text-lg font-medium mb-2">Official Provider</h3>
              <p className="text-sm text-muted-foreground">
                Official provider of event services for the Shaw Conference
                Centre in Edmonton.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
}
