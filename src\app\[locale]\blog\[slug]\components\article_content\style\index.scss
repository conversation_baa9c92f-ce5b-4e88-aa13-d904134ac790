.content {
  /* Heading styles */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: inherit;
    font-weight: bold;
    line-height: 1.2;
    margin-top: 0;
    margin-bottom: 0.5rem;
  }

  h1 {
    font-size: 2em; /* 32px */
  }
  h2 {
    font-size: 1.5em; /* 24px */
  }
  h3 {
    font-size: 1.25em; /* 20px */
  }
  h4 {
    font-size: 1.1em; /* 17.6px */
  }
  h5 {
    font-size: 1em; /* 16px */
  }
  h6 {
    font-size: 0.875em; /* 14px */
  }

  /* Paragraph styles */
  p {
    font-family: inherit;
    font-size: 1em; /* 16px */
    margin-top: 0;
    margin-bottom: 1rem;
  }

  /* Link styles */
  a {
    font-family: inherit;
    font-size: 1em; /* 16px */
    color: #0070f3;
    text-decoration: none;
    background-color: transparent;
  }

  a:hover {
    text-decoration: underline;
  }

  /* List styles */
  ul,
  ol {
    font-family: inherit;
    font-size: 1em; /* 16px */
    padding-left: 1.5rem;
    margin-top: 0;
    margin-bottom: 1rem;
  }

  li {
    font-family: inherit;
    font-size: 1em; /* 16px */
    margin-bottom: 0.5rem;
  }

  /* Table styles */
  table {
    font-family: inherit;
    font-size: 1em; /* 16px */
    border-collapse: collapse;
    width: 100%;
  }

  th,
  td {
    font-family: inherit;
    font-size: 1em; /* 16px */
    padding: 0.75rem;
    text-align: left;
    border: 1px solid #dee2e6;
  }

  thead th {
    font-family: inherit;
    font-size: 1em; /* 16px */
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
  }

  /* Button styles */
  button {
    font-family: inherit;
    font-size: 1em; /* 16px */
    line-height: inherit;
    margin: 0;
    padding: 0.5rem 1rem;
    background-color: transparent;
    border: none;
    cursor: pointer;
    appearance: none;
  }

  button:focus {
    outline: none;
  }

  /* Form styles */
  input,
  select,
  textarea {
    font-family: inherit;
    font-size: 1em; /* 16px */
    line-height: inherit;
    margin: 0;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    background-color: #fff;
  }

  input:focus,
  select:focus,
  textarea:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  /* Image styles */
  img {
    max-width: 100%;
    height: auto;
    border-style: none;
  }

  /* Other element styles */
  hr {
    border: 0;
    border-top: 1px solid #dee2e6;
    margin: 1.5rem 0;
  }

  blockquote {
    margin: 0 0 1rem;
    padding: 0.5rem 1rem;
    border-left: 0.25rem solid #dee2e6;
    color: #6c757d;
    font-size: 1.25em; /* 20px */
  }

  /* Address styles */
  address {
    font-style: normal;
    line-height: inherit;
  }

  /* Code and pre styles */
  code,
  kbd,
  pre,
  samp {
    font-family: monospace, monospace;
  }

  pre {
    margin-top: 0;
    margin-bottom: 1rem;
    overflow: auto;
    font-size: 0.875em; /* 14px */
  }
}
