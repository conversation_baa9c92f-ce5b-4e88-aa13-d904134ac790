import { MenuItem } from '@/models/types';
import { imageLoader } from '@/utils/image-loader';
import Image from 'next/image';
import Link from 'next/link';

function MegaDropdown({ name, children, description, image }: MenuItem) {
  return (
    <div className="w-full lg:min-w-[900px]  bg-primary lg:px-4  xl:px-8 pb-8  pt-12 z-50  ">
      <div className="w-full flex flex-col-reverse items-start lg:flex-row lg:gap-0  xl:gap-10   ">
        <div className="w-full px-2 lg:w-1/3  pt-5">
          <h2 className="text-3xl font-bold text-white mb-6">{name}</h2>
          <div className="flex flex-col gap-4">
            {children?.map((item, index) => (
              <Link
                key={index}
                href={item.href}
                className="text-white hover:text-secondary text-lg  duration-300 transition-colors"
              >
                {item.name}
              </Link>
            ))}
          </div>
        </div>
        <div className="w-full hidden lg:flex flex-col gap-3 lg:w-2/3 mb-8 lg:mb-0  overflow-hidden   ">
          {description && <p className="text-white text-lg ">{description}</p>}
          <div className="w-full hidden lg:flex  mb-8 lg:mb-0 aspect-[4/3]  rounded-xl overflow-hidden relative  ">
            {image && (
              <Image
                loader={imageLoader}
                src={image}
                alt={name}
                fill
                className="object-cover h-full w-full"
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default MegaDropdown;
