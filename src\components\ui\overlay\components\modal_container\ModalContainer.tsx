'use client';
import { ClassAttributes, FormHTMLAttributes, ReactNode } from 'react';
import './style/index.scss';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { Spinner } from '@/components/ui/spinner';

interface ModalContainerProps {
  title?: string;
  isLoading?: boolean;
  className?: string;
  onSubmit?: React.FormEventHandler<HTMLFormElement>;
  controls?: ReactNode;
  children?: ReactNode;
  description?: string;
  controlsPosition?: 'center' | 'end';
  overflowHidden?: boolean;
}
const Form = (
  props: ClassAttributes<HTMLFormElement> & FormHTMLAttributes<HTMLFormElement>,
) => <form {...props} />;
const Div = (
  props: ClassAttributes<HTMLDivElement> & React.HTMLAttributes<HTMLDivElement>,
) => <div {...props} />;

export default function ModalContainer({
  title,
  description,
  children,
  onSubmit,
  controls,
  isLoading,
  className = '',
  controlsPosition = 'end',
  overflowHidden,
}: ModalContainerProps) {
  const Container = onSubmit ? Form : Div;

  return (
    <Container onSubmit={onSubmit as any} className="modal-container">
      {isLoading ? (
        <Spinner className="w-20 h-20" />
      ) : (
        <Card className={cn('bg-grey ', className)}>
          <CardHeader>
            <CardTitle>{title}</CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </CardHeader>
          {children && (
            <CardContent
              className="modal-body"
              style={overflowHidden ? { overflow: 'hidden' } : undefined}
            >
              {children}
            </CardContent>
          )}
          {controls && (
            <CardFooter
              className={cn(
                'gap-2',
                controlsPosition == 'center' ? 'justify-center' : 'justify-end',
              )}
            >
              {controls}
            </CardFooter>
          )}
        </Card>
      )}
    </Container>
  );
}
