'use client';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import {
  sectionContainer,
  staggerContainer,
  staggerItem,
} from '@/lib/animations';

interface CTASectionProps {
  sectionId?: string;
  title?: string;
  subtitle?: string;
  buttonText?: string;
  buttonHref?: string;
  buttonTarget?: string;
  buttonRel?: string;
  className?: string;
}

export default function CTASection({
  sectionId = 'cta',
  title = 'Ready to elevate your next trade show?',
  subtitle = "Let's work together to create a memorable and successful event experience with GOODKEY SHOW SERVICES LTD.",
  buttonText = 'Order Now',
  buttonHref = 'https://admingoodkey.malopan.com/login',
  buttonTarget = '_blank',
  buttonRel = 'noopener noreferrer',
  className = '',
}: CTASectionProps) {
  return (
    <motion.section
      id={sectionId}
      className={`w-full py-12 md:py-24 bg-[#00646C] ${className}`}
      variants={sectionContainer}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
    >
      <motion.div
        className="container px-4 md:px-6 text-center overflow-x-hidden"
        variants={staggerContainer}
      >
        <motion.h2
          className="text-3xl font-bold tracking-tighter text-white mb-4"
          variants={staggerItem}
        >
          {title}
        </motion.h2>
        <motion.p
          className="text-white/90 max-w-[600px] mx-auto mb-8"
          variants={staggerItem}
        >
          {subtitle}
        </motion.p>
        <motion.div variants={staggerItem}>
          <Link href={buttonHref} target={buttonTarget} rel={buttonRel}>
            <Button className="bg-[#B10055] hover:bg-[#B10055]/90 text-white">
              {buttonText}
            </Button>
          </Link>
        </motion.div>
      </motion.div>
    </motion.section>
  );
}
