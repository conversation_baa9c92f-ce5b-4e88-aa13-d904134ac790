import type { Metadata } from 'next';
import '../../globals.scss';
import { ReactNode } from 'react';
import { Provider } from '@/components/app_provider/app-provider';
import { cn } from '@/lib/utils';
import AppHeader from '@/components/app_header';
import { OverlayContainer } from '@/components/ui/overlay/OverlayContainer';
import { Toaster } from '@/components/ui/toaster';
import Footer from '@/components/ui/app_layout/Footer';
import { inter } from '@/assets/fonts/fonts';
import MenuQuery from '@/services/queries/MenuQuery';
import { getQueryClient } from '@/utils/query-client';

export const metadata: Metadata = {
  title: 'GOODKEY SHOW SERVICES LTD. | Home',
  description:
    "GOODKEY SHOW SERVICES LTD. is Western Canada's premier trade show and event services provider. Offering booth design, installation, and rental services.",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  const header = await getQueryClient().fetchQuery({
    queryKey: ['header'],
    queryFn: MenuQuery.header,
  });
  const footer = await getQueryClient().fetchQuery({
    queryKey: ['footer'],
    queryFn: MenuQuery.footer,
  });

  return (
    <html suppressHydrationWarning>
      <body
        suppressHydrationWarning={true}
        className={cn(
          inter.className,
          ' flex flex-col min-h-screen overflow-x-hidden',
        )}
      >
        <Provider>
          <AppHeader items={header} />
          <main className="app flex flex-col min-h-[50vh] flex-1 overflow-x-hidden lg:mt-[80px]">
            {children}
          </main>
          <Toaster />
          <OverlayContainer />
          <Footer items={footer} />
        </Provider>
      </body>
    </html>
  );
}
