import MiddleContainer from '@/components/middle_container';
import { Metadata, ResolvingMetadata } from 'next';
import JobApplicationForm from './components/job_application_form';
import PageBanner from '@/components/page_banner';
import { getTranslations } from 'next-intl/server';
import { headers } from 'next/headers';

export async function generateMetadata(
  { params: { locale } }: { params: { locale: string } },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const t = await getTranslations({
    locale,
    namespace: 'Components.CareerPage',
  });
  return {
    title: t('metaDataTitle'),
    description: t('metaDataDesc'),
    metadataBase: new URL(
      process.env.APP_BASE_URL ?? `https://${headers().get('host')}`,
    ),
  };
}

export default async function Career() {
  const t = await getTranslations('Components.CareerPage');

  return (
    <div className="flex w-full flex-col gap-32 py-24">
      <PageBanner
        title={t('bannerTitle')}
        background="linear-gradient(to right, #8e5c7d, #2d4c5c)"
        items={[{ title: t('bannerSubTitle') }]}
      />
      <MiddleContainer>
        <JobApplicationForm />
      </MiddleContainer>
    </div>
  );
}
