import { QueryClient, QueryClientConfig } from '@tanstack/react-query';
import { cache } from 'react';

function makeQueryClient(config?: QueryClientConfig | undefined) {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // With SSR, we usually want to set some default staleTime
        // above 0 to avoid refetching immediately on the client
        staleTime: 60 * 1000,
      },
    },
    ...config,
  });
}

let browserQueryClient: QueryClient | undefined = undefined;

export function getQueryClient(cached = true) {
  if (typeof window === 'undefined') {
    // Server: always make a new query client
    return cached ? cache(makeQueryClient)() : makeQueryClient();
  } else {
    if (!browserQueryClient) browserQueryClient = makeQueryClient();
    return browserQueryClient;
  }
}
