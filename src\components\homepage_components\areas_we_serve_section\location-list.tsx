import { MapPin } from 'lucide-react';

interface Location {
  name: string;
  id?: string;
}

interface LocationListProps {
  title: string;
  locations: Location[];
  className?: string;
}

export default function LocationList({
  title,
  locations,
  className = '',
}: LocationListProps) {
  return (
    <div className={className}>
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <ul className="space-y-2 text-muted-foreground">
        {locations.map((location, index) => (
          <li key={location.id || index} className="flex items-center">
            <MapPin className="h-4 w-4 mr-2 text-[#00646C]" />
            {location.name}
          </li>
        ))}
      </ul>
    </div>
  );
}
