'use client';

import { ReactNode } from 'react';
import { Spinner } from './spinner';
import { Alert, AlertDescription, AlertTitle } from './alert';
import { AlertTriangleIcon } from 'lucide-react';

interface ISuspense {
  isLoading?: boolean;
  children?: ReactNode;
  error?: {
    message: string;
  };
}
export default function Suspense({ isLoading, children, error }: ISuspense) {
  return (
    <>
      {isLoading ? (
        <div className="flex h-full w-full items-center justify-center">
          <Spinner />
        </div>
      ) : error ? (
        <Alert className="mb-4" variant="destructive">
          <AlertTriangleIcon className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            <p>{error.message}</p>
          </AlertDescription>
        </Alert>
      ) : (
        children
      )}
    </>
  );
}
