import { themeColors } from '@/assets/color';
import { cn } from '@/lib/utils';

export default function Loading() {
  return (
    <div className="flex flex-col justify-center items-center w-full  min-h-[50vh] flex-1">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="50"
        height="50"
        viewBox="0 0 24 24"
        fill="none"
        stroke={themeColors.secondary.DEFAULT}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={cn('animate-spin')}
      >
        <path d="M21 12a9 9 0 1 1-6.219-8.56" />
      </svg>
    </div>
  );
}
