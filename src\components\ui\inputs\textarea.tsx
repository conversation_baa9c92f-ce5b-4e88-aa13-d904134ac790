import * as React from 'react';

import { cn } from '@/lib/utils';
import { useFormField } from '../form';

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, ...props }, ref) => {
    const { error } = useFormField();
    return (
      <textarea
        className={cn(
          'flex min-h-[40px] bg-transparent text-black  text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:border-[#1a7efb]  disabled:cursor-not-allowed disabled:opacity-50',
          'p-2 w-full border border-gray-300  rounded-md ',
          error && 'border-red-500 ',
          className,
        )}
        ref={ref}
        rows={4}
        {...props}
      />
    );
  },
);
Textarea.displayName = 'Textarea';

export { Textarea };
