import { cn } from '@/lib/utils';
import { ReactNode } from 'react';

interface ISectionPanel {
  title: string;
  titleClassName?: string;
  children: ReactNode;
}

function SectionPanel({ title, titleClassName, children }: ISectionPanel) {
  return (
    <section className="w-full flex flex-col gap-20 ">
      <h3
        className={cn(
          'text-3xl font-normal text-black w-full text-center ',
          titleClassName,
        )}
      >
        {title}
      </h3>
      {children}
    </section>
  );
}

export default SectionPanel;
