import Link from 'next/link';
import { ChevronRight } from 'lucide-react';
import { services } from '../../data/data';
import { getTranslations } from 'next-intl/server';

export default async function ServicesSidebar() {
  const s = await getTranslations('Components.servicesList');
  return (
    <nav className="rounded-lg bg-[#1a2d5a] p-4">
      {services.map((service) => (
        <Link
          key={service.slug}
          href={`/services/${service.slug}`}
          className="flex items-center justify-between rounded-md px-4 py-2.5 text-sm text-white transition-colors hover:bg-white/10"
        >
          {s(service.titleKey)}
          <ChevronRight className="h-4 w-4" />
        </Link>
      ))}
    </nav>
  );
}
