import { ReactNode } from 'react';

interface ILabelValueRow {
  title: string;
  value: string | ReactNode;
}

function LabelValueRow({ title, value }: ILabelValueRow) {
  return (
    <div className="text-pair flex flex-col sm:flex-row  gap-5  items-start  ">
      <span className="font-bold text-[15px] min-w-[200px] overflow-hidden text-ellipsis     ">
        {title}
      </span>
      {typeof value === 'string' ? (
        <span className="font-normal text-[14px]  overflow-hidden text-ellipsis   ">
          {value}
        </span>
      ) : (
        value
      )}
    </div>
  );
}

export default LabelValueRow;
