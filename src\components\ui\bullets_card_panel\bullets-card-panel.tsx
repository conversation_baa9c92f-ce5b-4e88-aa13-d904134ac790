'use client';
import CardGenerator from '@/components/ui/card_generator';
import { motion } from 'framer-motion';
import BulletCard from '../bullet_card';

const fadeInVariant = {
  hidden: { opacity: 0, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.2,
      duration: 0.6,
    },
  }),
};
interface IBulletsCardPanel {
  title: string;
  description?: string;
  list: {
    title: string;
    bulletsList: string[];
  }[];
}

function BulletsCardPanel({ title, description, list }: IBulletsCardPanel) {
  return (
    <section className="bullets-card-panel">
      <CardGenerator
        className="border-none shadow-none gap-16"
        headerClassName="p-0"
        description={description}
        title={
          <h4 className="text-primary text-2xl leading-[45px] font-medium max-w-sm">
            {title}
          </h4>
        }
      >
        <div className="w-full xl:w-fit xl:self-center grid grid-cols-1 xl:grid-cols-2 gap-x-32 gap-y-16 min-h-[500px]">
          {list.map((item, index) => (
            <motion.div
              key={index}
              custom={index}
              initial="hidden"
              whileInView="visible"
              variants={fadeInVariant}
              viewport={{ once: true, amount: 0.2 }}
            >
              <BulletCard title={item.title} list={item.bulletsList} />
            </motion.div>
          ))}
        </div>
      </CardGenerator>
    </section>
  );
}

export default BulletsCardPanel;
