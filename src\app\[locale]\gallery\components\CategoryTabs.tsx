import { TabsList, TabsTrigger } from '@/components/ui/tabs';
import { GalleryCategory } from '@/models/GalleryModels';
import { FaArrowRight } from 'react-icons/fa6';

interface CategoryTabsProps {
  categories: GalleryCategory[];
  value: string;
  onValueChange: (value: string) => void;
}

export default function CategoryTabs({
  categories,
  value,
  onValueChange,
}: CategoryTabsProps) {
  return (
    <TabsList className="flex flex-col justify-start items-start gap-2   h-full self-stretch">
      {categories.map((cat) => (
        <TabsTrigger
          key={cat.id}
          value={cat.id.toString()}
          className="group whitespace-normal justify-start items-center gap-5 text-left w-full px-0 ml-0 py-2  data-[state=active]:text-white data-[state=active]:font-bold data-[state=active]:text-2xl data-[state=inactive]:text-white/80 data-[state=inactive]:font-normal data-[state=inactive]:text-lg transition-all"
        >
          <FaArrowRight className="text-white/80 text-3xl group-data-[state=active]:text-white" />
          {cat.name}
        </TabsTrigger>
      ))}
    </TabsList>
  );
}
