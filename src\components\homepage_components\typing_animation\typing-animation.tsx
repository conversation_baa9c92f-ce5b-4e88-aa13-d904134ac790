'use client';

import React, { ReactNode, useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

interface TypingAnimationProps {
  text: string | ReactNode;
  duration?: number;
  className?: string;
}

export default function TypingAnimation({
  text,
  duration = 200,
  className,
}: TypingAnimationProps) {
  const [displayedLength, setDisplayedLength] = useState<number>(0);

  // Function to recursively apply the typing effect to a ReactNode
  const applyTypingEffect = (
    node: ReactNode,
    currentLength: number,
  ): [ReactNode, number] => {
    if (typeof node === 'string') {
      return [
        node.slice(0, currentLength),
        Math.max(0, currentLength - node.length),
      ];
    }

    if (React.isValidElement(node)) {
      let remainingLength = currentLength;
      const newChildren = React.Children.map(node.props.children, (child) => {
        const [processedChild, remaining] = applyTypingEffect(
          child,
          remainingLength,
        );
        remainingLength = remaining;
        return processedChild;
      });

      return [React.cloneElement(node, {}, newChildren), remainingLength];
    }

    if (Array.isArray(node)) {
      let remainingLength = currentLength;
      const newArray = node.map((item) => {
        const [processedItem, remaining] = applyTypingEffect(
          item,
          remainingLength,
        );
        remainingLength = remaining;
        return processedItem;
      });

      return [newArray, remainingLength];
    }

    return [node, currentLength];
  };

  useEffect(() => {
    const totalLength = JSON.stringify(text).length;
    const typingEffect = setInterval(() => {
      setDisplayedLength((prevLength) => {
        if (prevLength < totalLength) {
          return prevLength + 1;
        } else {
          clearInterval(typingEffect);
          return prevLength;
        }
      });
    }, duration);

    return () => {
      clearInterval(typingEffect);
    };
  }, [text, duration]);

  const [displayedContent] = applyTypingEffect(text, displayedLength);

  return (
    <div className={cn('drop-shadow-sm', className)}>{displayedContent}</div>
  );
}
