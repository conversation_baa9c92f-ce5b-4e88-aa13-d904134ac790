export interface AppointmentDetails {
  date: Date;
  appointmentTimeId: number;
  surveyTypeId: number;
  propertyTypeId: number;
  qst1?: string | null;
  qst2?: string | null;
  otherType?: string | null;
}

export interface AppointmentTimeBrief {
  id: number;
  startTime: string;
  endTime: string;
}

type RequestSurvey = {
  appointmentTime: AppointmentTimeBrief;
  date: string; // ISO date string
  propertyType: string;
  surveyType: string;
  qst1?: string;
  qst2?: string;
};

export type QuoteRequest = {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  movingFrom: string;
  movingTo: string;
  movingDate: string;
  requestSurvey: RequestSurvey;
};
