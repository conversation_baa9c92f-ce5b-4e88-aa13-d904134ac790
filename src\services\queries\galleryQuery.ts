import fetcher from './fetcher';
import {
  GalleryCategory,
  GallerySubcategory,
  GalleryImage,
} from '@/models/GalleryModels';

const galleryQuery = {
  tags: ['gallery'] as const,

  getCategories: async () => fetcher<GalleryCategory[]>(`gallery/categories`),

  getSubcategoriesByCategory: async (categoryId: number) =>
    fetcher<GallerySubcategory[]>(
      `gallery/categories/${categoryId}/subcategories`,
    ),

  getImagesBySubcategory: async (subcategoryId: number) =>
    fetcher<GalleryImage[]>(`gallery/subcategories/${subcategoryId}/images`),

  getImageById: async (id: number) =>
    fetcher<GalleryImage>(`gallery/images/${id}`),

  downloadImage: async (id: number) => {
    const response = await fetch(`/api/gallery/images/${id}/download`);
    if (!response.ok) {
      throw new Error('Image download failed');
    }
    return response.blob();
  },
};

export default galleryQuery;
