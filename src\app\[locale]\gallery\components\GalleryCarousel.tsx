'use client';
import { useState, useEffect } from 'react';
import { GalleryImage } from '@/models/GalleryModels';
import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react';
import AppImage from '@/components/ui/app_image';
import { imageLoader } from '@/utils/image-loader';

interface GalleryCarouselProps {
  items: GalleryImage[];
  className?: string;
}

function getAspectRatioClass(width: number, height: number) {
  const ratio = width / height;
  const tolerance = 0.1;
  if (Math.abs(ratio - 1.5) < tolerance) {
    return 'aspect-[3/2]';
  } else if (Math.abs(ratio - 1.333) < tolerance) {
    return 'aspect-[4/3]';
  } else {
    return 'aspect-[3/2]';
  }
}

export default function GalleryCarousel({
  items,
  className = '',
}: GalleryCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [imageDimensions, setImageDimensions] = useState<{
    width: number;
    height: number;
  } | null>(null);
  const [showLoader, setShowLoader] = useState(true);

  useEffect(() => {
    setShowLoader(true);
    setImageDimensions(null);
    if (!items[currentIndex]?.url) return;
    const imgUrl = imageLoader({ src: items[currentIndex].url });
    const img = new window.Image();
    img.onload = () => {
      setImageDimensions({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
      setTimeout(() => setShowLoader(false), 300);
    };
    img.src = imgUrl;
  }, [currentIndex, items]);

  const goToPrevious = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? items.length - 1 : prevIndex - 1,
    );
  };

  const goToNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === items.length - 1 ? 0 : prevIndex + 1,
    );
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  if (!items.length) return null;

  const currentItem = items[currentIndex];

  const aspectRatioClass = imageDimensions
    ? getAspectRatioClass(imageDimensions.width, imageDimensions.height)
    : 'aspect-[3/2]';
  const isLoading = showLoader || !imageDimensions;

  return (
    <div className={`relative w-full ${className}`}>
      {/* Main Image */}
      <div
        className={`relative w-full ${aspectRatioClass} rounded-lg overflow-hidden flex items-center justify-center`}
      >
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/70 z-10">
            <Loader2 className="animate-spin h-8 w-8 text-gray-400" />
          </div>
        )}
        <AppImage
          image={{
            path: currentItem.url,
            alt: currentItem.alt || currentItem.name || '',
            name: currentItem.name,
          }}
          nextImageClassName="object-cover"
          style={
            isLoading
              ? { opacity: 0 }
              : { opacity: 1, transition: 'opacity 0.2s' }
          }
        />

        {/* Navigation Arrows */}
        {items.length > 1 && (
          <>
            <button
              onClick={goToPrevious}
              className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md z-10 transition-colors"
            >
              <ChevronLeft className="h-6 w-6 text-gray-700" />
              <span className="sr-only">Previous</span>
            </button>
            <button
              onClick={goToNext}
              className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md z-10 transition-colors"
            >
              <ChevronRight className="h-6 w-6 text-gray-700" />
              <span className="sr-only">Next</span>
            </button>
          </>
        )}

        {/* Caption */}
        <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white p-4">
          <p>{currentItem.alt || currentItem.name}</p>
        </div>
      </div>

      {/* Pagination Dots */}
      {items.length > 1 && (
        <div className="flex justify-center mt-4 space-x-2">
          {items.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentIndex ? 'bg-[#00646C]' : 'bg-gray-300'
              }`}
              aria-current={index === currentIndex ? 'true' : 'false'}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Thumbnails */}
      {items.length > 1 && (
        <div className="grid grid-cols-4 gap-2 mt-4">
          {items.map((item, index) => (
            <button
              key={item.id}
              onClick={() => goToSlide(index)}
              className={`aspect-square relative rounded overflow-hidden border-2 transition-colors ${
                index === currentIndex
                  ? 'border-[#00646C]'
                  : 'border-transparent hover:border-gray-300'
              }`}
            >
              <AppImage
                image={{
                  path: item.url,
                  alt: item.alt || item.name || '',
                  name: item.name,
                }}
                nextImageClassName="object-cover"
                fill
                className="object-cover"
              />
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
