import PageBanner from '@/components/page_banner';
import AboutIntroSection from './components/about-intro-section';
import { Metadata, ResolvingMetadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { headers } from 'next/headers';
import JustOneCallSection from './components/just-one-call-section';
import CTASection from '@/components/homepage_components/cta_section';
import Image from 'next/image';

export async function generateMetadata(
  { params: { locale } }: { params: { locale: string } },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const t = await getTranslations({
    locale,
    namespace: 'Components.aboutUsPage',
  });
  return {
    title: t('metaDataTitle'),
    description: t('metaDataDesc'),
    metadataBase: new URL(
      process.env.APP_BASE_URL ?? `https://${headers().get('host')}`,
    ),
  };
}

export default async function AboutUs({
  params,
  searchParams,
}: {
  params: { slug: string };
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  return (
    <div className="w-full flex flex-col gap-10  overflow-x-hidden  self-center max-w-[1920px]   ">
      <PageBanner
        title={'About Us'}
        backgroundImage="/page-banner-background.jpg"
        description={
          <div className="flex items-center gap-4">
            <span className="text-white text-lg md:text-2xl text-left max-w-3xl drop-shadow-md">
              A Proud Canadian Company Since 1980
            </span>
            <Image
              src="/about-us/about-us-banner-icon.png"
              alt="About Us Icon"
              width={48}
              height={48}
              className="w-12 h-12 object-contain"
            />
          </div>
        }
      />
      <AboutIntroSection />
      <JustOneCallSection />
      <CTASection />
    </div>
  );
}
