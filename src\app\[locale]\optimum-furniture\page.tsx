import PageBanner from '@/components/page_banner';
import FurnitureGrid from '@/components/furniture-grid';
import Link from 'next/link';
import { optimumFurnitureItems } from './data/furniture-data';

export default async function OptimumFurniture() {
  return (
    <div className="w-full flex flex-col gap-10  overflow-x-hidden  self-center max-w-[1920px]  pb-24 ">
      <PageBanner
        title={'Optimum Furniture'}
        backgroundImage="/page-banner-background.jpg"
        overlayColor="#00646CE3"
        classname="h-[650px] md:h-[400px] lg:h-[500px]"
        description={
          <div className="flex flex-col gap-1">
            <p className="text-white text-lg md:text-xl text-left max-w-[900px] font-light  drop-shadow-md">
              If you demand the highest quality and want to add a level of
              sophistication to your exhibit, choose the Optimum Furniture
              Collection by Goodkey. A stylish contemporary collection of the
              finest crafted Leather furniture. We have carefully selected this
              collection based on style and comfort to create your optimum
              image.
            </p>
            <Link
              href="/#"
              className="text-white text-lg md:text-xl  hover:underline font-normal hover:text-secondary"
            >
              [Please follow the link to view more rental furnishings]
            </Link>
          </div>
        }
      />

      <FurnitureGrid items={optimumFurnitureItems} />
    </div>
  );
}
