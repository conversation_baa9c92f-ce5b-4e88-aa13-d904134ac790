import { cn } from '@/lib/utils';
import { BreadCrumbType } from '../ui/bread_crumb/bread-crumb';
import { ReactNode } from 'react';

interface IPageBanner {
  title: string;
  description?: string | ReactNode;
  items?: BreadCrumbType[];
  background?: string;
  backgroundImage?: string;
  overlay?: boolean;
  overlayOpacity?: number;
  overlayColor?: string;
  classname?: string;
}

async function PageBanner({
  title,
  description,
  items,
  background,
  backgroundImage,
  overlay = true,
  overlayOpacity = 0.5,
  overlayColor,
  classname,
}: IPageBanner) {
  return (
    <section
      className={cn(
        'page-banner relative w-full flex flex-col gap-10 items-center justify-center h-[200px] md:h-[250px] lg:h-[360px]',
        classname,
      )}
    >
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: backgroundImage
            ? `url(${backgroundImage})`
            : undefined,
          background: !backgroundImage ? background : undefined,
        }}
      ></div>

      {/* Overlay */}
      {overlay && backgroundImage && (
        <div
          className="absolute inset-0"
          style={{
            background: overlayColor || 'rgba(0, 0, 0, 0.70)',
          }}
        ></div>
      )}
      {overlay && !backgroundImage && background && (
        <div
          className="absolute inset-0 bg-black"
          style={{
            background: background,
            opacity: overlayOpacity,
          }}
        ></div>
      )}
      <div className="flex  flex-col  gap-10   justify-between  w-full container mx-auto self-center z-10 ">
        <h1 className="text-white text-3xl md:text-4xl lg:text-5xl font-medium text-left drop-shadow-lg">
          {title}
        </h1>

        {description && (
          <div className="w-full flex flex-col justify-center mt-4">
            {typeof description === 'string' ? (
              <p className="text-white text-lg md:text-2xl text-left max-w-3xl drop-shadow-md">
                {description}
              </p>
            ) : (
              description
            )}
          </div>
        )}
      </div>
    </section>
  );
}

export default PageBanner;
