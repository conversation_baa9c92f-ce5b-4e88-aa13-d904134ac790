'use client';
import { ImageData } from '@/models/ImageData';
import { formatDate } from 'date-fns';
import { useTranslations } from 'next-intl';
import AppImage from '../../../../../components/ui/app_image';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/Badge';
import { FaCalendarAlt } from 'react-icons/fa';
import { FaTags } from 'react-icons/fa6';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

interface IArticleCard {
  id: number;
  index: number;
  articleNumber: number;
  title: string;
  summary?: string;
  updatedAt?: Date;
  createdAt: Date;
  image?: ImageData;
  slug: string;
  tags?: string;
  className?: string;
}

function ArticleCard({
  id,
  index,
  articleNumber,
  title,
  summary,
  updatedAt,
  createdAt,
  image,
  slug,
  tags,
  className,
}: IArticleCard) {
  const c = useTranslations('Components.common');
  return (
    <div
      className={cn(
        ' w-full    transition-all duration-300 rounded-xl  flex flex-col justify-between overflow-hidden ',
        ' min-h-[500px] max-h-[500px]  ',
        // 'xl:min-w-[320px] xl:max-w-[320px] ',
        // ' 2xl:min-w-[400px] 2xl:max-w-[400px]',
        className,
      )}
    >
      <div
        className={cn(
          ' w-full h-full  bg-white  transition-all duration-300 rounded-xl rounded-br-none  flex flex-col justify-between overflow-hidden ',
          'bg-gray-100 hover:bg-gray-300',
        )}
      >
        <div className={cn('flex   h-full  flex-col')}>
          <div
            className={cn(
              'rounded-xl   w-full relative max-h-[240px] min-h-[240px] ',
            )}
          >
            <AppImage
              className="rounded-xl w-full h-full object-cover"
              image={image}
            />
          </div>
          <div className={cn(' w-full h-full flex flex-col gap-3 p-5  ')}>
            <h5 className=" text-xl font-bold   text-gray-900 ">{title}</h5>
            <div className="w-full inline-flex flex-wrap flex-row gap-2 items-center ">
              {createdAt && (
                <Badge
                  variant={'transparent'}
                  className="inline-flex flex-row items-baseline gap-2 px-0 "
                >
                  <FaCalendarAlt className="text-secondary size-4 " />
                  <span className=" font-semibold text-left text-foreground/50 text-sm ">
                    {formatDate(createdAt, 'dd/MM/yyyy')}
                  </span>
                </Badge>
              )}
              <div className="inline-flex flex-row gap-2 flex-wrap items-center  ">
                {tags &&
                  tags.split(',').map((tag, i) => (
                    <Badge
                      key={i}
                      variant="transparent"
                      className="text-foreground/50 text-sm font-medium flex flex-row items-baseline gap-2 px-0"
                    >
                      <FaTags className="text-secondary size-4 " />
                      {tag}
                    </Badge>
                  ))}
              </div>
            </div>
            {summary && (
              <p className=" font-normal text-gray-500   line-clamp-2 overflow-hidden text-ellipsis  max-h-[50px] h-full ">
                {summary}
              </p>
            )}
          </div>
        </div>
      </div>
      <div className=" w-full flex flex-row items-center justify-end ">
        <Link
          href={`/blog/${slug}`}
          className="flex justify-center items-center group w-fit  self-end  "
        >
          <Button
            variant={'default'}
            className="w-full gap-2 text-white rounded-none rounded-b-xl uppercase px-5 h-10 text-md font-medium transition-all duration-300 "
          >
            {c('readMore')}
          </Button>
        </Link>
      </div>
    </div>
  );
}

export default ArticleCard;
