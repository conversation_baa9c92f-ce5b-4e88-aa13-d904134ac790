'use client';
import PortfolioCarousel from './portfolio-carousel';
import { motion } from 'framer-motion';
import { fadeInUp, sectionContainer } from '@/lib/animations';

interface PortfolioItem {
  id: string;
  src: string;
  alt: string;
  caption: string;
  thumbnail?: string;
}

interface OurWorkSectionProps {
  sectionId?: string;
  title?: string;
  subtitle?: string;
  portfolioItems?: PortfolioItem[];
  className?: string;
}

const defaultPortfolioItems: PortfolioItem[] = [
  {
    id: 'cim-magazine',
    src: '/cim-magazine-booth.jpeg',
    alt: 'GOODKEY SHOW SERVICES LTD. - CIM Magazine trade show booth',
    caption:
      'CIM Magazine trade show booth with overhead circular signage and branded reception counter',
  },
  {
    id: 'snc-lavalin',
    src: '/snc-lavalin-booth.jpeg',
    alt: 'GOODKEY SHOW SERVICES LTD. - SNC-Lavalin booth',
    caption:
      'SNC-Lavalin professional trade show booth with modern design and interactive displays',
  },
  {
    id: 'dux-mining',
    src: '/dux-mining-booth.jpeg',
    alt: 'GOODKEY SHOW SERVICES LTD. - DUX Mining booth',
    caption:
      'DUX Mining trade show booth featuring industrial design elements and product showcases',
  },
  {
    id: 'axxis-bme',
    src: '/axxis-bme-booth.jpeg',
    alt: 'GOODKEY SHOW SERVICES LTD. - AXXIS BME booth',
    caption:
      'AXXIS BME booth with contemporary styling and strategic branding placement',
  },
];

export default function OurWorkSection({
  sectionId = 'work',
  title = 'Our Work',
  subtitle = 'Browse our portfolio of trade show booths and event setups designed and installed by GOODKEY SHOW SERVICES LTD.',
  portfolioItems = defaultPortfolioItems,
  className = '',
}: OurWorkSectionProps) {
  return (
    <motion.section
      id={sectionId}
      className={`w-full py-12 md:py-24 bg-gray-50 ${className}`}
      variants={sectionContainer}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
    >
      <div className="container px-4 md:px-6 overflow-x-hidden">
        <motion.div
          className="text-center max-w-[800px] mx-auto mb-12"
          variants={fadeInUp}
        >
          <h2 className="text-3xl font-bold tracking-tighter mb-4 text-[#5C4B35]">
            {title}
          </h2>
          <p className="text-gray-700 text-lg">{subtitle}</p>
        </motion.div>

        <PortfolioCarousel items={portfolioItems} />
      </div>
    </motion.section>
  );
}
