export async function GET(
  request: Request,
  { params: { slug } }: { params: { slug: string[]; locale: string } },
) {
  const fullUrl = `${process.env.API_BASE_URL}/api/${slug.join('/')}`;
  const respond = await fetch(fullUrl, {
    ...request,
  });
  return respond;
}
export async function POST(
  request: Request,
  { params: { slug } }: { params: { slug: string[]; locale: string } },
) {
  const fullUrl = `${process.env.API_BASE_URL}/api/${slug.join('/')}`;
  const requestHeaders = new Headers(request.headers);
  const headers = {
    'content-type': requestHeaders.get('content-type'),
    //TODO add as needed
  };
  const isFormData = requestHeaders
    .get('content-type')
    ?.includes('multipart/form-data');
  const respond = await fetch(fullUrl, {
    body: isFormData
      ? await request.formData()
      : JSON.stringify(await request.json()),
    method: request.method,
    headers: Object.fromEntries(
      Object.entries(headers).filter(
        ([_, value]) => value !== undefined && value !== '',
      ),
    ) as Record<string, string>,
    mode: request.mode,
    integrity: request.integrity,
    redirect: request.redirect,
    referrer: request.referrer,
    referrerPolicy: request.referrerPolicy,
  });
  return respond;
}
