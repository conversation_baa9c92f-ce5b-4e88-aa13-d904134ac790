'use server';
import nodemailer from 'nodemailer';
import Mail from 'nodemailer/lib/mailer';
import mustache from 'mustache';
import { template } from '@/utils/email-template';

interface RecaptchaResult {
  success: boolean;
  score: number;
  action: string;
  hostname: string;
}

function validateRecaptchaResult(
  result: RecaptchaResult,
  minimumScore: number = 0.8,
): boolean {
  if (!result.success) {
    console.error('reCAPTCHA verification failed');
    return false;
  }

  if (result.score < minimumScore) {
    console.error(`reCAPTCHA score too low: ${result.score}`);
    return false;
  }

  return true;
}

export async function verifyRecaptcha(
  recaptchaToken: string,
): Promise<boolean> {
  try {
    const recaptchaVerification = await fetch(
      `https://www.google.com/recaptcha/api/siteverify?secret=${process.env.RECAPTCHA_SECRET_KEY}&response=${recaptchaToken}`,
      { method: 'POST' },
    );
    const recaptchaResult: RecaptchaResult = await recaptchaVerification.json();
    console.log({ recaptchaResult });

    return validateRecaptchaResult(recaptchaResult);
  } catch (error) {
    console.error('reCAPTCHA verification error:', error);
    return false;
  }
}

export async function sendEmail(data: FormData) {
  if (
    !process.env.EMAIL_FROM ||
    !process.env.EMAIL_SERVER_URL ||
    !process.env.EMAIL_SERVER_PORT ||
    !process.env.EMAIL_SERVER_USER ||
    !process.env.EMAIL_SERVER_PASSWORD
  ) {
    return {
      error: true,
      message: 'Email not sent',
      status: 500,
    };
  }

  const name = data.get('name');
  const subject = data.get('subject');
  const email = data.get('email');
  const phoneNumber = data.get('phoneNumber');
  const message = data.get('message');
  // const companyName = data.get('companyName');
  const recaptchaToken = data.get('recaptchaToken');

  if (typeof recaptchaToken !== 'string') {
    return {
      error: true,
      message: 'Invalid reCAPTCHA token',
      status: 400,
    };
  }

  console.log({
    name,
    subject,
    email,
    phoneNumber,
    message,
  });

  try {
    const isRecaptchaValid = await verifyRecaptcha(recaptchaToken);
    if (!isRecaptchaValid) {
      return {
        error: true,
        message: 'reCAPTCHA verification failed',
        status: 400,
      };
    }
  } catch (error) {
    console.error('reCAPTCHA verification error:', error);
    return {
      error: true,
      message: 'reCAPTCHA verification failed',
      status: 500,
    };
  }

  const transport = nodemailer.createTransport({
    host: process.env.EMAIL_SERVER_URL,
    port: Number(process.env.EMAIL_SERVER_PORT) || 465,
    secure: true,
    auth: {
      user: process.env.EMAIL_SERVER_USER,
      pass: process.env.EMAIL_SERVER_PASSWORD,
    },
  });

  const mailOptions: Mail.Options = {
    from: process.env.EMAIL_FROM,
    // to: process.env.EMAIL_TO,
    to: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
    // subject: subject?.toString() || 'General Request',
    subject: `Message from ${name?.toString().split(' ')[0] + ' ' + name?.toString().split(' ')[1]} (${email})`,
    html: mustache.render(template, {
      fromType: process.env.EMAIL_FROM,
      name,
      subject,
      email,
      phoneNumber,
      message,
    }),
  };

  const sendMailPromise = () =>
    new Promise<string>((resolve, reject) => {
      transport.sendMail(mailOptions, function (err) {
        if (!err) {
          resolve('Email sent');
        } else {
          reject(err.message);
        }
      });
    });

  try {
    await sendMailPromise();
    console.log('Email sent');
    return {
      success: true,
      message: 'Email sent',
      status: 200,
    };
  } catch (err) {
    console.log(err);
    return {
      error: true,
      message: 'Email not sent',
      status: 500,
    };
  }
}
