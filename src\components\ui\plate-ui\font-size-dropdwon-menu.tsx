'use client';

import type { DropdownMenuProps } from '@radix-ui/react-dropdown-menu';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  useOpenState,
} from './dropdown-menu';
import { ToolbarButton } from './toolbar';
import {
  focusEditor,
  getMark,
  setMarks,
  useEditorState,
} from '@udecode/plate-common';
import { Ruler } from 'lucide-react';
import { cn } from '@/lib/utils';

const FONT_SIZES = [
  6, 8, 9, 10, 11, 12, 14, 16, 18, 20, 24, 28, 32, 36, 40, 48,
];

export function FontSizeDropdownMenu(props: DropdownMenuProps) {
  const editor = useEditorState();
  const openState = useOpenState();

  return (
    <DropdownMenu modal={false} {...openState} {...props}>
      <DropdownMenuTrigger asChild>
        <ToolbarButton pressed={openState.open} tooltip="Font size">
          {(getMark(editor, 'fontSize') as string) || (
            <Ruler className={cn('w-4 h-4')} />
          )}
        </ToolbarButton>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align="start"
        className="flex max-h-[500px] min-w-[180px] flex-col gap-0.5 overflow-y-auto"
      >
        {FONT_SIZES.map((fontSize) => (
          <DropdownMenuItem
            key={fontSize}
            onSelect={() => {
              setMarks(editor, { fontSize });
              focusEditor(editor);
            }}
          >
            {fontSize}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
