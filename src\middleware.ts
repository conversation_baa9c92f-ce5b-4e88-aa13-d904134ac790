import createMiddleware from 'next-intl/middleware';
import { localePrefix, locales, pathnames } from './i18n.config';

export default createMiddleware({
  // A list of all locales that are supported
  locales: locales,
  pathnames: pathnames,
  defaultLocale: 'en',
  localePrefix: localePrefix,
});

export const config = {
  matcher: [
    // Enable a redirect to a matching locale at the root
    '/',

    // Set a cookie to remember the previous locale for
    // all requests that have a locale prefix
    '/(fr|en)/:path*',

    // Enable redirects that add missing locales
    // (e.g. `/pathnames` -> `/en/pathnames`)
    '/((?!_next|_vercel|.*\\..*).*)',
  ],
};
