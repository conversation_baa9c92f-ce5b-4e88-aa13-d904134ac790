import { ReactNode } from 'react';
interface IContactFooter {
  controls: ReactNode;
  title: string;
  description: string;
}

function ContactFooter({ controls, title, description }: IContactFooter) {
  return (
    <div className="flex flex-col gap-6 items-center md:items-start text-center px-5 md:pl-10 w-full bg-[#E8E8E8] py-8  self-center max-w-[1140px] ">
      <h2 className="text-2xl md:text-3xl font-medium text-gray-900  ">
        {title}
      </h2>
      <p className="text-base font-semibold text-gray-900 ">{description}</p>
      <div className="flex flex-col tb:flex-row gap-3 tb:gap-4 justify-center">
        {controls}
      </div>
    </div>
  );
}

export default ContactFooter;
