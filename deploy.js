const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '../../.env.local' });
require('dotenv').config({ path: '../../.env.staging' });
require('dotenv').config({ path: '.env.production' });
require('dotenv').config({ path: '../../.env' });

const RUN_DIR = process.cwd();

const {
  APP_NAME,
  APP_DIR,
  REPO,
  BRANCH = 'main',
  ENV = 'production',
  DEPLOY_PORT,
  STORAGE_DIR,
} = process.env;

console.log('App Name:', APP_NAME);
console.log('App Dir:', APP_DIR);
console.log('Repo:', REPO);
console.log('Branch:', BRANCH);
console.log('Environment:', ENV);
console.log('Port:', DEPLOY_PORT);
console.log('Storage Directory:', STORAGE_DIR);
console.log('Run Dir:', RUN_DIR);
const deploy = async () => {
  try {
    process.chdir(path.join(APP_DIR, 'current'));
    const lastCommit = execSync('git rev-parse HEAD').toString().trim();
    console.log(`${lastCommit} is last commit`);
    /*
    // Create directory for new release
    const date = new Date().toISOString().replace(/[^0-9]/g, '');
    const releaseDir = path.join(APP_DIR, 'releases', date);
    console.log(`Creating ${releaseDir}`);
    fs.mkdirSync(releaseDir, { recursive: true });
    process.chdir(releaseDir);

    // Clone the repo
    console.log(`Cloning ${REPO} into ${releaseDir}`);
    execSync(`git clone -b ${BRANCH} ${REPO} .`);

    // Check if it's a new commit
    const newCommit = execSync('git rev-parse HEAD').toString().trim();
    console.log(`${newCommit} is new commit`);
    if (lastCommit === newCommit) {
      console.log('Commit is same hash, aborting!!');
      fs.copyFileSync(
        path.join(RUN_DIR, 'latest_deploy.log'),
        path.join(APP_DIR, 'latest_deploy.log'),
      );
      fs.rmdirSync(releaseDir, { recursive: true });
      process.exit(1);
    }

    // Create symbolic links
    const dotenv = ENV ? `.${ENV}` : '';
    console.log(`Linking ${path.join(APP_DIR, `.env${dotenv}`)} to .env`);
    fs.symlinkSync(
      path.join(APP_DIR, `.env${dotenv}`),
      path.join(releaseDir, '.env'),
      'file',
    );
    if (STORAGE_DIR) {
      console.log(
        `Linking ${path.join(releaseDir, 'storage')} to ${STORAGE_DIR}`,
      );
      fs.symlinkSync(STORAGE_DIR, path.join(releaseDir, 'storage'), 'dir');
    }

    // Install npm packages and build
    console.log('Installing npm packages');
    execSync('npm install');
    console.log(`Building ${releaseDir} ${dotenv}`);
    execSync(`dotenv -e .env${dotenv} -- npx next build`);

    // Update current symlink
    console.log(`Linking ${releaseDir} to ${APP_DIR}/current`);
    fs.unlinkSync(path.join(APP_DIR, 'current'));
    fs.symlinkSync(releaseDir, path.join(APP_DIR, 'current'), 'dir');

    // Restart the node server
    console.log(`Restarting node server for ${APP_NAME} at ${releaseDir}`);
    process.chdir(releaseDir);
    execSync(`pm2 delete ${APP_NAME}`);
    execSync(`lsof -i tcp:${PORT} | awk 'NR!=1 {print $2}' | xargs kill`);
    execSync(
      `pm2 start npx --time --name="${APP_NAME}" --no-treekill --node-args="--max-old-space-size=3096" -- next start -- --port=${PORT}`,
    );

    // Delete older releases
    const releasesDir = path.join(APP_DIR, 'releases');
    const releases = fs
      .readdirSync(releasesDir)
      .filter((file) =>
        fs.statSync(path.join(releasesDir, file)).isDirectory(),
      );
    const toRemove = releases
      .slice(0, -4)
      .map((rel) => path.join(releasesDir, rel));
    console.log('Removing:', toRemove.join(', '));
    toRemove.forEach((rel) => fs.rmdirSync(rel, { recursive: true }));

    // Update deploy script
    console.log('Updating deploy script');
    fs.chmodSync(path.join(releaseDir, 'deploy.js'), '755');
    fs.copyFileSync(
      path.join(releaseDir, 'deploy.js'),
      path.join(APP_DIR, 'deploy.js'),
    );
    fs.chmodSync(path.join(APP_DIR, 'deploy.js'), '755');

    console.log('Deployed!');
*/
  } catch (error) {
    console.error('Deployment failed:', error);
    process.exit(1);
  }
};

deploy();
