import {
  ArticleCategory,
  ArticleCategoryBrief,
} from '@/models/ArticleCategory';
import fetcher from './fetcher';
import { BriefData } from '@/models/BriefData';

const ArticleCategoriesQuery = {
  tags: ['articleCategories'] as const,
  getBrief: async () => fetcher<BriefData[]>(`BlogCategories/getBrief`),
  getAll: async () => fetcher<ArticleCategoryBrief[]>(`BlogCategories`),
  get: async (id: number) => {
    return fetcher<ArticleCategory>(`BlogCategories/${id}`);
  },
};
export default ArticleCategoriesQuery;
