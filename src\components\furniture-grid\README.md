# FurnitureGrid Component

A reusable React component for displaying furniture items in a responsive grid layout.

## Features

- **Responsive Design**: Adapts to different screen sizes with configurable column layouts
- **TypeScript Support**: Fully typed with proper interfaces
- **Hover Effects**: Smooth transitions and interactive states
- **Customizable**: Flexible styling and layout options
- **Accessible**: Proper alt text and semantic HTML structure

## Usage

```tsx
import FurnitureGrid from '@/components/furniture-grid';
import { FurnitureItem } from '@/components/furniture-grid';

const furnitureItems: FurnitureItem[] = [
  {
    id: 'chair-1',
    name: 'Modern Chair',
    subtitle: 'Black Leather',
    image: '/images/chair.jpg',
    alt: 'Modern black leather chair'
  },
  // ... more items
];

export default function MyPage() {
  return (
    <FurnitureGrid 
      items={furnitureItems}
      columns={{ mobile: 2, tablet: 3, desktop: 4 }}
      className="my-custom-class"
    />
  );
}
```

## Props

### FurnitureGrid

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `items` | `FurnitureItem[]` | Required | Array of furniture items to display |
| `className` | `string` | `undefined` | Additional CSS classes for the container |
| `columns` | `ColumnConfig` | `{ mobile: 2, tablet: 3, desktop: 4 }` | Number of columns for different screen sizes |

### FurnitureItem

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `id` | `string` | Yes | Unique identifier for the item |
| `name` | `string` | Yes | Main title of the furniture item |
| `subtitle` | `string` | Yes | Subtitle or description |
| `image` | `string` | Yes | Path to the item image |
| `alt` | `string` | No | Alt text for the image (auto-generated if not provided) |

## Styling

The component uses Tailwind CSS classes and includes:
- Responsive grid layouts
- Hover effects with smooth transitions
- Card-style design with shadows and borders
- Brand color integration for hover states

## Best Practices

1. **Image Optimization**: Use Next.js optimized images with appropriate dimensions
2. **Alt Text**: Provide descriptive alt text for accessibility
3. **Unique IDs**: Ensure each furniture item has a unique ID
4. **Responsive Images**: Consider different image sizes for different screen sizes
5. **Loading States**: Consider adding loading states for better UX

## Examples

### Basic Usage
```tsx
<FurnitureGrid items={furnitureItems} />
```

### Custom Column Layout
```tsx
<FurnitureGrid 
  items={furnitureItems}
  columns={{ mobile: 1, tablet: 2, desktop: 3 }}
/>
```

### With Custom Styling
```tsx
<FurnitureGrid 
  items={furnitureItems}
  className="bg-gray-100 p-8"
/>
```
