'use client';

import { cn } from '@/lib/utils';
import { ReactNode, useRef } from 'react';
import { motion, useInView } from 'framer-motion';

interface ISideContentDescription {
  leftContent: ReactNode;
  rightContent?: ReactNode;
  rightContentClassName?: string;
  leftContentClassName?: string;
  className?: string;
  reverse?: boolean;
  isAnimated?: boolean;
}

function SideContentDescription({
  leftContent,
  rightContent,
  rightContentClassName,
  leftContentClassName,
  className,
  reverse = false,
  isAnimated = false,
}: ISideContentDescription) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });

  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const leftItemVariants = {
    hidden: { opacity: 0, x: reverse ? 100 : -100 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { type: 'spring', stiffness: 100, damping: 15 },
    },
  };

  const rightItemVariants = {
    hidden: { opacity: 0, x: reverse ? -100 : 100 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { type: 'spring', stiffness: 100, damping: 15 },
    },
  };

  const MotionWrapper = isAnimated ? motion.div : 'div';

  return (
    <MotionWrapper
      ref={ref}
      className={cn(
        'flex flex-col xl:flex-row justify-between xl:gap-12 self-center w-full',
        reverse ? 'xl:flex-row-reverse' : 'xl:flex-row',
        className,
      )}
      variants={isAnimated ? containerVariants : undefined}
      initial="hidden"
      animate={isAnimated && isInView ? 'visible' : 'hidden'}
    >
      <MotionWrapper
        className={cn(
          'flex flex-col justify-center items-center min-w-[50%] w-full',
          leftContentClassName,
        )}
        variants={isAnimated ? leftItemVariants : undefined}
        custom={false}
      >
        {leftContent}
      </MotionWrapper>
      <MotionWrapper
        className={cn(
          'flex flex-col justify-start items-center w-full',
          rightContentClassName,
        )}
        variants={isAnimated ? rightItemVariants : undefined}
        custom={true}
      >
        {rightContent}
      </MotionWrapper>
    </MotionWrapper>
  );
}

export default SideContentDescription;
