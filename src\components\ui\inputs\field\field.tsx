import { HTMLInputTypeAttribute, InputHTMLAttributes } from 'react';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../../form';
import { Input } from '../input';
import MultiSelectFormField from '../MultiBadgeSelector';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../select';
import { Textarea } from '../textarea';
import {
  Control,
  ControllerRenderProps,
  FieldPath,
  FieldValues,
} from 'react-hook-form';
import PhoneNumberInput from '../phone_number_input';
import Checkbox from '../checkbox';
import {
  FileInput,
  FileUploader,
  FileUploaderContent,
  FileUploaderItem,
} from '../../FileInput';
import { DropzoneOptions } from 'react-dropzone';
import FileSvgDraw from '../../file_svg_draw';
import Image from 'next/image';
import { RadioGroup, RadioGroupItem } from '../../radio-group';
import { cn } from '@/lib/utils';
import { FileText } from 'lucide-react';
import { DateTimePicker } from '../date-picker/datetime-picker';

export type OptionType = { label: string; value: string };
type RenderTypeOption = {
  label: string;
  value: string;
  render?: (
    field: ControllerRenderProps<FieldValues, FieldPath<FieldValues>>,
  ) => React.ReactNode;
};

interface MultiBadgeSelectOptionType extends OptionType {
  icon?: React.ReactNode;
}

type multiBadgeSelectType = {
  type: 'multiBadgeSelect';
  props: {
    options: MultiBadgeSelectOptionType[];
    placeholder: string;
  };
};
type multiSelectType = {
  type: 'multiSelect';
  props: {
    options: RenderTypeOption[];
    placeholder: string;
  };
};
type selectType = {
  type: 'select';
  props: {
    options: OptionType[];
    placeholder: string;
  };
};

type RadioGroupType = {
  type: 'RadioGroup';
  props: {
    options: RenderTypeOption[];
  };
};

type fileType = {
  type: 'file';
  props: {
    dropzoneOptions: DropzoneOptions;
  };
};

export type InputType =
  | HTMLInputTypeAttribute
  | (
      | 'text'
      | 'phone'
      | 'textarea'
      | 'richText'
      | 'checkbox'
      | RadioGroupType
      | 'month'
      | fileType
      | multiBadgeSelectType
      | multiSelectType
      | selectType
      | 'date'
    );

interface IField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> {
  type: InputType;
  control: Control<TFieldValues>;
  name: TName;
  label?: string;
  description?: string;
  required?: boolean;
}
function Field<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  type,
  name,
  label,
  description,
  required,
  control,
  ...otherInputProps
}: IField<TFieldValues, TName> &
  Omit<InputHTMLAttributes<HTMLInputElement>, 'type'>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && (
            <FormLabel>
              {label}
              {required && <span className="text-red-500"> *</span>}
            </FormLabel>
          )}
          {description && <FormDescription>{description}</FormDescription>}
          <FormControl>
            {type == 'textarea' ? (
              <Textarea {...field} />
            ) : type == 'phone' ? (
              <PhoneNumberInput {...field} />
            ) : type == 'date' ? (
              <DateTimePicker value={field.value} onChange={field.onChange} />
            ) : type == 'checkbox' ? (
              <Checkbox
                checked={field.value}
                onCheckedChange={field.onChange}
              ></Checkbox>
            ) : typeof type == 'object' ? (
              type.type == 'file' ? (
                <FileUploader
                  value={field.value}
                  onValueChange={field.onChange}
                  dropzoneOptions={type.props.dropzoneOptions}
                  reSelect={true}
                  className="  rounded-lg"
                >
                  {field.value && field.value.length > 0 ? (
                    <FileUploaderContent>
                      {field.value &&
                        field.value.length > 0 &&
                        field.value.map((file: File, i: number) => (
                          <FileUploaderItem key={i} index={i}>
                            <div className="flex flex-row items-center gap-2  ">
                              {file.type.startsWith('image') ? (
                                <Image
                                  src={URL.createObjectURL(file)}
                                  alt={file.name}
                                  width={100}
                                  height={100}
                                  className="object-cover rounded-md size-[72px] "
                                />
                              ) : (
                                <div className="size-[72px] flex items-center justify-center rounded-md bg-gray-600">
                                  <FileText className="size-full p-4 text-primary " />
                                </div>
                              )}

                              <div className="flex flex-col gap-2    ">
                                <span className="text-sm w-fit  truncate   ">
                                  {file.name}
                                </span>
                                <span className="text-sm w-fit">
                                  {Math.round(file.size / 1024)} KB
                                </span>
                              </div>
                            </div>
                          </FileUploaderItem>
                        ))}
                    </FileUploaderContent>
                  ) : (
                    <FileInput className="outline-dashed outline-1 outline-gray-300">
                      <div className="flex items-center justify-center flex-col pt-3 pb-4 w-full ">
                        <FileSvgDraw
                          accept={
                            type.props.dropzoneOptions.accept ?? undefined
                          }
                        />
                      </div>
                    </FileInput>
                  )}
                </FileUploader>
              ) : type.type == 'multiSelect' ? (
                <div className="flex flex-row gap-8 flex-wrap w-full">
                  {type.props.options.map((option, index) => (
                    <FormField
                      key={index}
                      control={control}
                      name={name}
                      render={({ field: checkboxField }) => {
                        return (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <label className="checkbox-container flex items-center cursor-pointer  relative ">
                                <Checkbox
                                  className="absolute right-4 top-4 size-6 z-10"
                                  checked={checkboxField.value?.includes(
                                    option.value,
                                  )}
                                  onCheckedChange={(checked) => {
                                    return checked
                                      ? checkboxField.onChange([
                                          ...checkboxField.value,
                                          option.value,
                                        ])
                                      : checkboxField.onChange(
                                          checkboxField.value?.filter(
                                            (v: string) => v !== option.value,
                                          ),
                                        );
                                  }}
                                ></Checkbox>
                                {option.render &&
                                  option.render(checkboxField as any)}
                              </label>
                            </FormControl>
                          </FormItem>
                        );
                      }}
                    />
                  ))}
                </div>
              ) : type.type == 'multiBadgeSelect' ? (
                <MultiSelectFormField
                  onValueChange={field.onChange}
                  placeholder={type.props.placeholder}
                  options={type.props.options}
                  {...otherInputProps}
                  defaultValue={
                    typeof field.value == 'string' ||
                    typeof field.value == 'number'
                      ? [field.value]
                      : (field.value as any[])
                  }
                />
              ) : type.type == 'RadioGroup' ? (
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="flex flex-row gap-8 flex-wrap w-full"
                >
                  {type.props.options.map((option, index) => (
                    <FormItem
                      key={index}
                      className="flex flex-row items-center w-fit  relative "
                    >
                      <FormLabel className="font-normal  ">
                        {option.label}
                        {option.render && option.render(field as any)}
                      </FormLabel>
                      <FormControl>
                        <RadioGroupItem
                          className={cn(
                            option.render && 'absolute top-4 right-4  z-10 ',
                          )}
                          value={option.value}
                        />
                      </FormControl>
                    </FormItem>
                  ))}
                </RadioGroup>
              ) : (
                type.type == 'select' && (
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={type.props.placeholder} />
                    </SelectTrigger>
                    <SelectContent>
                      {type.props.options.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )
              )
            ) : (
              <Input {...field} {...otherInputProps} />
            )}
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export default Field;
