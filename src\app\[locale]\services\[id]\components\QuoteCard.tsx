import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { getTranslations } from 'next-intl/server';
import Link from 'next/link';

export default async function QuoteCard() {
  const c = await getTranslations('Components.common');
  return (
    <Card className="bg-gradient-to-br from-[#1a2d5a] to-[#ff4d6d] border-card-border">
      <CardContent className="p-6">
        <div className="flex flex-col gap-4 text-center text-white">
          <h3 className="text-xl font-bold leading-tight">
            {c('getQuoteDesc')}
          </h3>
          <Link href="/#quote">
            <Button
              size="lg"
              variant="secondary"
              className="w-full bg-[#ff4d6d] text-white hover:bg-[#ff4d6d]/90"
            >
              {c('getQuote')}
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
