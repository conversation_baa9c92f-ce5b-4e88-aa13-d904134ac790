import { GallerySubcategory, GalleryImage } from '@/models/GalleryModels';
import GalleryCarousel from './GalleryCarousel';

interface GallerySubcategorySectionProps {
  subcategory: GallerySubcategory;
  images: GalleryImage[];
}

export default function GallerySubcategorySection({
  subcategory,
  images,
}: GallerySubcategorySectionProps) {
  return (
    <div className="mb-12">
      <div className="text-base text-muted-foreground mb-4 font-medium">
        {subcategory.description}
      </div>

      <GalleryCarousel items={images} />
    </div>
  );
}
