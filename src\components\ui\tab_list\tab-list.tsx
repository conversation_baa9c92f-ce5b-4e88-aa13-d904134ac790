import { Carousel, CarouselContent } from '../carousel';
import { TabsList } from '../tabs';
import { ReactNode } from 'react';

interface ITabList {
  children: ReactNode;
}

function ScrollableTabList({ children }: ITabList) {
  return (
    <TabsList className="w-full">
      <Carousel className="w-full flex gap-2" opts={{ dragFree: true }}>
        <CarouselContent>{children}</CarouselContent>
      </Carousel>
    </TabsList>
  );
}

export default ScrollableTabList;
