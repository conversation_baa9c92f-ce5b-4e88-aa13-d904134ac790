export interface Location {
  lat: number;
  lng: number;
}

export const fetchGeoLocation = async (address: string) => {
  const response = await fetch(
    `https://maps.googleapis.com/maps/api/geocode/json?address=${address}&key=${process.env.NEXT_PUBLIC_Google_API_Key}`,
  );
  if (!response.ok) {
    throw new Error(`HTTP error! Status: ${response.status}`);
  }
  const data = await response.json();

  return data.results?.[0]?.geometry?.location as Location;
};

export default fetchGeoLocation;
