import { cn } from '@/lib/utils';

interface ITestimonialItem {
  name: string;
  text: string;
  suffix?: string;
}

function TestimonialItem({ name, text, suffix }: ITestimonialItem) {
  return (
    <div
      className={cn(
        'z-20    flex flex-col ',
        'w-full ',
        'bg-black/50 p-4 rounded-xl border border-gray-500 shadow-lg',
      )}
    >
      <div
        className={cn(
          'flex flex-col gap-5 sm:gap-6 text-left',
          'w-full bg-custom-gray   ',
          ' py-10 px-4  rounded-xl border border-secondary/50 shadow-lg',
          '   md:min-w-[200px]  ',
        )}
      >
        <p className="text-white  text-sm md:text-lg  text-left  ">{text}</p>
        <div className="w-full border-white border-dashed border   "></div>
        <h2 className="text-white text-xl md:text-4xl  font-medium   ">
          {name}
        </h2>
        {suffix && (
          <p className="text-white/80  text-sm md:text-lg  text-left  ">
            {text}
          </p>
        )}
      </div>
    </div>
  );
}

export default TestimonialItem;
