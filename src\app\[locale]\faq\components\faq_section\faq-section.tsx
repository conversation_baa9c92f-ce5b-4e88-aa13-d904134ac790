'use client';

import { useEffect, useState } from 'react';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import FaqTabList from '../faq_tab_list';
import FaqAccordion from '../faq_accordion';
import { useQuery } from '@tanstack/react-query';
import faqQuery from '@/services/queries/faqQuery';
import Suspense from '@/components/ui/Suspense';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import Image from 'next/image';

interface IFaqSection {}

const ITEMS_PER_PAGE = 10;

function FaqSection({}: IFaqSection) {
  const { data, isLoading } = useQuery({
    queryKey: [...faqQuery.tags],
    queryFn: faqQuery.get,
  });

  const [currentPage, setCurrentPage] = useState(1);
  const [activeTab, setActiveTab] = useState('');

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  useEffect(() => {
    if (data) {
      setActiveTab(data[0].name);
    }
  }, [data]);
  const renderPagination = (totalItems: number) => {
    const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE);

    return (
      <div className="bg-white border-t border-gray-200 p-4 flex justify-center items-center space-x-4">
        <Button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          variant="outline"
        >
          <ChevronLeft className="h-4 w-4" />
          Previous
        </Button>
        <span>
          Page {currentPage} of {totalPages}
        </span>
        <Button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          variant="outline"
        >
          Next
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    );
  };

  return (
    <Suspense isLoading={isLoading}>
      {data && (
        <div className="w-full  flex flex-col lg:flex-row gap-8 lg:gap-4  self-center max-w-[1400px] pb-32 px-4   ">
          <div className="w-full   ">
            {/* <h1 className="text-4xl font-bold mb-16 text-center">FAQ</h1> */}
            <Tabs
              defaultValue={data[0].name}
              orientation="vertical"
              onValueChange={(value) => {
                setActiveTab(value);
                setCurrentPage(1);
              }}
              className="flex flex-col md:flex-row w-full  gap-5 "
            >
              <div className=" w-full md:w-fit flex flex-col-reverse  md:flex-col gap-5 ">
                <FaqTabList categories={data.map((section) => section.name)} />
                <div className="w-full  relative  aspect-[4/3]   rounded-lg overflow-hidden ">
                  <Image src={'/faq/faq-image.jpeg'} alt="faq-image" fill />
                </div>
              </div>
              <div className="w-full flex flex-col items-start justify-start  ">
                {data.map((section) => (
                  <TabsContent
                    className="w-full"
                    key={section.id}
                    value={section.name}
                  >
                    <div className="pb-16">
                      <FaqAccordion
                        questions={section.items.slice(
                          (currentPage - 1) * ITEMS_PER_PAGE,
                          currentPage * ITEMS_PER_PAGE,
                        )}
                      />
                    </div>
                  </TabsContent>
                ))}
                <div className="w-full">
                  {activeTab &&
                    data.length >= ITEMS_PER_PAGE &&
                    renderPagination(
                      data.find((section) => section.name === activeTab)?.items
                        .length || 0,
                    )}
                </div>
              </div>
            </Tabs>
          </div>
        </div>
      )}
    </Suspense>
  );
}

export default FaqSection;
