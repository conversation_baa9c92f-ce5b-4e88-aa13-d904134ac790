'use client';
import { But<PERSON> } from '@/components/ui/button';
import LocationList from './location-list';
import { motion } from 'framer-motion';
import { fadeInLeft, fadeInRight, sectionContainer } from '@/lib/animations';
import GoogleMap from '@/components/ui/google_map';

interface Location {
  name: string;
  id?: string;
}

interface LocationGroup {
  title: string;
  locations: Location[];
  id?: string;
}

interface AreasWeServeSectionProps {
  sectionId?: string;
  badge?: string;
  title?: string;
  subtitle?: string;
  locationGroups?: LocationGroup[];
  otherRegionsTitle?: string;
  otherRegionsDescription?: string;
  ctaButtonText?: string;
  ctaButtonHref?: string;
  className?: string;
}

const defaultLocationGroups: LocationGroup[] = [
  {
    id: 'alberta',
    title: 'Alberta',
    locations: [
      { id: 'calgary', name: 'Calgary' },
      { id: 'edmonton', name: 'Edmonton' },
      { id: 'red-deer', name: 'Red Deer' },
    ],
  },
  {
    id: 'british-columbia',
    title: 'British Columbia',
    locations: [
      { id: 'vancouver', name: 'Vancouver' },
      { id: 'kelowna', name: 'Kelown<PERSON>' },
      { id: 'victoria', name: 'Victoria' },
    ],
  },
];

export default function AreasWeServeSection({
  sectionId = 'areas-we-serve',
  badge = 'Our Reach',
  title = 'Areas We Serve',
  subtitle = 'Call GOODKEY SHOW SERVICES LTD. for Trade Show Event Services in Vancouver, Edmonton & Beyond',
  locationGroups = defaultLocationGroups,
  otherRegionsTitle = 'Other Regions',
  otherRegionsDescription = 'Regina, Saskatoon, Winnipeg, Yukon, and more',
  ctaButtonText = 'Contact Us',
  ctaButtonHref = '#',
  className = '',
}: AreasWeServeSectionProps) {
  return (
    <motion.section
      id={sectionId}
      className={`w-full py-12 md:py-24 bg-white ${className}`}
      variants={sectionContainer}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
    >
      <div className="container px-4 md:px-6 overflow-x-hidden">
        <div className="grid md:grid-cols-2 gap-12 items-center overflow-x-hidden">
          <motion.div variants={fadeInLeft}>
            <div className="inline-block rounded-lg bg-[#00646C]/10 px-3 py-1 text-sm text-[#00646C] font-medium mb-2">
              {badge}
            </div>
            <h2 className="text-3xl font-bold tracking-tighter mb-4">
              {title}
            </h2>
            <p className="text-muted-foreground mb-6">{subtitle}</p>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {locationGroups.map((group, index) => (
                <LocationList
                  key={group.id || index}
                  title={group.title}
                  locations={group.locations}
                />
              ))}
            </div>

            {otherRegionsTitle && otherRegionsDescription && (
              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-2">
                  {otherRegionsTitle}
                </h3>
                <p className="text-muted-foreground">
                  {otherRegionsDescription}
                </p>
              </div>
            )}

            <Button
              className="mt-8 bg-[#00646C] hover:bg-[#00646C]/90 text-white"
              onClick={() => (window.location.href = ctaButtonHref)}
            >
              {ctaButtonText}
            </Button>
          </motion.div>
          <motion.div
            className="relative rounded-lg overflow-hidden"
            variants={fadeInRight}
          >
            <GoogleMap
              height="400px"
              className="w-full"
              center={{ lat: 52.5, lng: -110 }}
              zoom={5}
            />
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
}
