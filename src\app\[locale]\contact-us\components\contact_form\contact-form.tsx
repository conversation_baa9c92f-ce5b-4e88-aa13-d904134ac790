'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useTranslations } from 'next-intl';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

import { Check, X } from 'lucide-react';
import { ReloadIcon } from '@radix-ui/react-icons';
import { cn } from '@/lib/utils';
import { verifyRecaptcha } from '@/services/queries/email';
import { useMutation } from '@tanstack/react-query';
import RequestQuery from '@/services/queries/contact';
import { Textarea } from '@/components/ui/inputs/textarea';

export const contactFormSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
  phoneNumber: z.string().min(1, 'Phone number is required'),
  message: z.string().optional(),
});

export default function ContactForm() {
  const c = useTranslations('Components.common');
  const t = useTranslations('Components.Contact');
  const [loading, setLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | undefined>(undefined);
  const { executeRecaptcha } = useGoogleReCaptcha();

  const form = useForm<z.infer<typeof contactFormSchema>>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      message: '',
    },
  });

  const {
    isSuccess: isEmailSuccess,
    isError: isEmailError,
    isPending: isEmailPending,
    mutateAsync: SendContactMessage,
    error: emailError,
  } = useMutation({
    mutationKey: RequestQuery.tags,
    mutationFn: RequestQuery.SendContactMessage,
  });

  async function onSubmit(data: z.infer<typeof contactFormSchema>) {
    if (!executeRecaptcha) {
      setError('Recaptcha not yet available.');
      return;
    }

    setLoading(true);

    try {
      const token = await executeRecaptcha('submit_contact_form');

      if (!token) {
        setError('Captcha verification failed. Please try again.');
        return;
      }

      const isRecaptchaValid = await verifyRecaptcha(token);

      if (isRecaptchaValid) {
        const response = await SendContactMessage(data);

        if (!response) {
          setError('There was an error submitting the form. Please try again.');
        }
        setIsSuccess(true);
      } else {
        setError('Recaptcha verification failed. Please try again.');
      }

      form.reset();
    } catch (err) {
      console.error(' form submission error:', err);
      setError('There was an error submitting the form. Please try again.');
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="w-full  p-8 duration-500 transition-all  max-w-[750px] self-center  ">
      <h2 className="text-3xl font-bold text-center mb-8">
        {t('contactTitle')}
      </h2>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('firstName')} *</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder={t('firstNamePlaceholder')} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('lastName')} *</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder={t('lastNamePlaceholder')} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('email')} *</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      placeholder={t('emailPlaceholder')}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('phoneNumber')} *</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="tel"
                      placeholder={t('phonePlaceholder')}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="message"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('message')}</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder={t('messagePlaceholder')}
                    className="h-32"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="flex justify-center">
            <Button
              type="submit"
              className={cn(
                'w-full max-w-xs',
                error
                  ? 'bg-red-600 hover:bg-red-700'
                  : 'bg-secondary hover:bg-primary/90',
              )}
              disabled={loading}
            >
              {loading || isEmailPending ? (
                <ReloadIcon className="mr-2 h-6 w-6 animate-spin" />
              ) : isSuccess && isEmailSuccess ? (
                <>
                  <Check className="mr-2 h-6 w-6" />
                  {c('success')}
                </>
              ) : error || isEmailError ? (
                <>
                  <X className="mr-2 h-6 w-6" />
                  {error ? c('error') : emailError && emailError?.message}
                </>
              ) : (
                t('submit')
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
