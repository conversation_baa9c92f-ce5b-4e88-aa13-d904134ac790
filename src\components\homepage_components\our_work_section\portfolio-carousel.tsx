'use client';

import { useState } from 'react';
import Image from 'next/image';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface PortfolioItem {
  id: string;
  src: string;
  alt: string;
  caption: string;
  thumbnail?: string;
}

interface PortfolioCarouselProps {
  items: PortfolioItem[];
  className?: string;
}

export default function PortfolioCarousel({
  items,
  className = '',
}: PortfolioCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  const goToPrevious = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? items.length - 1 : prevIndex - 1,
    );
  };

  const goToNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === items.length - 1 ? 0 : prevIndex + 1,
    );
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  if (!items.length) return null;

  const currentItem = items[currentIndex];

  return (
    <div className={`relative max-w-5xl mx-auto ${className}`}>
      {/* Main Image */}
      <div className="relative rounded-lg overflow-hidden">
        <div className="aspect-[16/9] relative">
          <Image
            src={currentItem.src}
            alt={currentItem.alt}
            fill
            className="object-cover"
            unoptimized
          />
        </div>

        {/* Navigation Arrows */}
        {items.length > 1 && (
          <>
            <button
              onClick={goToPrevious}
              className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md z-10 transition-colors"
            >
              <ChevronLeft className="h-6 w-6 text-gray-700" />
              <span className="sr-only">Previous</span>
            </button>
            <button
              onClick={goToNext}
              className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md z-10 transition-colors"
            >
              <ChevronRight className="h-6 w-6 text-gray-700" />
              <span className="sr-only">Next</span>
            </button>
          </>
        )}

        {/* Caption */}
        <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white p-4">
          <p>{currentItem.caption}</p>
        </div>
      </div>

      {/* Pagination Dots */}
      {items.length > 1 && (
        <div className="flex justify-center mt-4 space-x-2">
          {items.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentIndex ? 'bg-[#00646C]' : 'bg-gray-300'
              }`}
              aria-current={index === currentIndex ? 'true' : 'false'}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Thumbnails */}
      {items.length > 1 && (
        <div className="grid grid-cols-4 gap-2 mt-4">
          {items.map((item, index) => (
            <button
              key={item.id}
              onClick={() => goToSlide(index)}
              className={`aspect-square relative rounded overflow-hidden border-2 transition-colors ${
                index === currentIndex
                  ? 'border-[#00646C]'
                  : 'border-transparent hover:border-gray-300'
              }`}
            >
              <Image
                src={item.thumbnail || item.src}
                alt={`${item.alt} thumbnail`}
                fill
                className="object-cover"
                unoptimized
              />
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
