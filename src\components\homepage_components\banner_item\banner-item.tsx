'use client';
import { cn } from '@/lib/utils';
import { ImageData } from '@/models/ImageData';
import { useTranslations } from 'next-intl';
import { ReactNode } from 'react';
import Image from 'next/image';

interface IBannerItem {
  image: ImageData;
  className?: string;
  children?: ReactNode;
}

function BannerItem({ image, className, children }: IBannerItem) {
  const c = useTranslations('Components.common');

  return (
    <div
      className={cn(
        'flex flex-col items-center justify-start py-5 gap-4    w-full h-full text-left  relative overflow-hidden ',
        className,
      )}
    >
      {/* <div
        className="absolute top-0 left-0 right-0 bottom-0  object-cover   bg-scroll  md:bg-fixed  "
        style={{
          backgroundImage: `url(${image.path})`,
          backgroundSize: 'contain',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      /> */}
      <Image
        src={image.path}
        alt={image.name ?? 'banner'}
        fill
        className=" object-cover   "
        // style={{
        //   backgroundImage: `url(${image.path})`,
        //   backgroundSize: 'contain',
        //   backgroundPosition: 'center',
        //   backgroundRepeat: 'no-repeat',
        // }}
      />
      {children}
    </div>
  );
}

export default BannerItem;
