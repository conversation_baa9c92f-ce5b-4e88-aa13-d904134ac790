'use client';

import Link from 'next/link';
import { useState, useTransition, useEffect } from 'react';

import { Button } from '@/components/ui/button';
import { useLocale } from 'next-intl';
import { usePathname, useRouter } from '@/utils/navigation';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '../ui/accordion';
import { cn } from '@/lib/utils';
import { ChevronDownIcon } from 'lucide-react';
import HamburgerButton from '../ui/hamburger_button';
import Image from 'next/image';
import { ctaButton } from '@/config/navigation';
import { MenuItem } from '@/models/types';

interface AppHeaderProps {
  items: MenuItem[];
}

function AppHeader({ items }: AppHeaderProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [_isPending, startTransition] = useTransition();

  // Handle scroll detection for sticky header
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 50); // Trigger after 50px scroll
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  function switchLanguage(lang: string) {
    startTransition(() => {
      router.replace(pathname, { locale: lang });
    });
  }

  // Use the provided items or fall back to empty array
  const navigationLinks = items;

  return (
    <>
      <header
        className={`lg:fixed top-0 z-50 w-full bg-white/95 transition-all duration-300 ${
          isScrolled
            ? 'border-b border-border/40 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/90 shadow-md'
            : 'border-b border-border/40 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/90 lg:border-transparent '
        }`}
      >
        <div className="container flex h-16 sm:h-18 md:h-20 items-center justify-between min-w-0">
          <div className="flex items-center gap-2">
            <Link href="/" className="flex items-center">
              <div className="relative w-28 xs:w-32 sm:w-36 md:w-40 lg:w-44 xl:w-48 aspect-logo min-w-[112px] max-w-[192px]">
                <Image
                  src="/gSS-logo-main.png"
                  alt="GOODKEY SHOW SERVICES LTD. Logo"
                  fill
                  className="object-contain"
                  priority
                  sizes="(max-width: 640px) 128px, (max-width: 768px) 144px, (max-width: 1024px) 160px, (max-width: 1280px) 176px, 192px"
                />
              </div>
            </Link>
          </div>
          <nav className="hidden lg:flex items-center">
            {navigationLinks.map((item) => (
              <div key={item.name} className="relative group px-2 xl:px-3">
                {item.children && item.children.length > 0 ? (
                  <>
                    <div className="flex items-center gap-1 py-2 text-xs lg:text-sm font-medium text-[#0A0A0A] hover:text-[#00646C] transition-colors cursor-pointer whitespace-nowrap">
                      <span className="hidden lg:inline">{item.name}</span>
                      <span className="lg:hidden">
                        {(item as any).shortName || item.name}
                      </span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-4 w-4 transition-transform group-hover:rotate-180"
                      >
                        <path d="m6 9 6 6 6-6" />
                      </svg>
                    </div>
                    <div className="absolute left-0 top-full z-50 mt-1 w-48 rounded-md border border-gray-100 bg-white py-2 shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all">
                      {item.children.map((child) => (
                        <Link
                          key={child.name}
                          href={child.href}
                          className="block px-4 py-2 text-sm hover:bg-gray-50 text-[#0A0A0A]"
                        >
                          {child.name}
                        </Link>
                      ))}
                    </div>
                  </>
                ) : (
                  <Link
                    href={item.href}
                    className="flex items-center gap-1 py-2 text-xs lg:text-sm font-medium text-[#0A0A0A] hover:text-[#00646C] transition-colors whitespace-nowrap"
                  >
                    <span className="hidden lg:inline">{item.name}</span>
                    <span className="lg:hidden">
                      {(item as any).shortName || item.name}
                    </span>
                  </Link>
                )}
              </div>
            ))}

            <div className="ml-2 lg:ml-4">
              <Link
                href={ctaButton.href}
                target={ctaButton.target}
                rel={ctaButton.rel}
              >
                <Button className="bg-[#B10055] hover:bg-[#B10055]/90 text-white text-xs lg:text-sm px-3 lg:px-4 py-1.5 lg:py-2">
                  {ctaButton.text}
                </Button>
              </Link>
            </div>
          </nav>

          <div className="lg:hidden">
            <HamburgerButton
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              isOpen={mobileMenuOpen}
            />
          </div>
        </div>
      </header>

      <div className="z-50 w-full overflow-y-auto bg-white lg:hidden">
        <Accordion
          type="single"
          collapsible
          className="flex lg:hidden"
          value={mobileMenuOpen ? 'menu' : undefined}
        >
          <AccordionItem value="menu" className="w-full">
            <AccordionContent>
              <Accordion type="single" collapsible className="w-full ">
                {navigationLinks.map((item) => {
                  if (item.children && item.children.length > 0) {
                    return (
                      <AccordionItem
                        key={item.name}
                        value={item.name}
                        className={cn(
                          'border-b border-b-primary w-full group ',
                        )}
                      >
                        <AccordionTrigger
                          className={cn(
                            'flex  item-center justify-between gap-0 px-4 py-3  font-medium transition-all [&[data-state=open]>svg]:rotate-180 [&[data-state=open]]:bg-gray-100 w-full group-hover:bg-gray-100 group-hover:text-primary',
                          )}
                        >
                          {item.href ? (
                            <Link href={item.href} className="hover:underline">
                              {item.name}
                            </Link>
                          ) : (
                            item.name
                          )}
                          <ChevronDownIcon className="h-4 w-4 shrink-0 text-primary transition-transform duration-200 group-hover:text-secondary " />
                        </AccordionTrigger>
                        <AccordionContent className="overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down">
                          <div className="grid  grid-cols-2 tb:grid-cols-1   pt-5 pb-3 ">
                            {item.children.map((child) => (
                              <Link
                                key={child.name}
                                className="group w-full inline-flex h-9  items-center justify-start rounded-md px-4 py-6  transition-colors hover:bg-gray-100 hover:text-gray-900 focus:bg-gray-100 focus:text-gray-900 focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-gray-100/50 data-[state=open]:bg-gray-100/50 dark:bg-gray-950 dark:hover:bg-gray-800 dark:hover:text-gray-50 dark:focus:bg-gray-800 dark:focus:text-gray-50 dark:data-[active]:bg-gray-800/50 dark:data-[state=open]:bg-gray-800/50"
                                href={child.href}
                              >
                                <Button
                                  variant={'ghost'}
                                  onClick={() => {
                                    setMobileMenuOpen(!mobileMenuOpen);
                                  }}
                                  className="w-full text-sm font-medium  text-left justify-start "
                                >
                                  {child.name}
                                </Button>
                              </Link>
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    );
                  } else {
                    return (
                      <AccordionItem
                        key={item.name}
                        className={cn(
                          'border-b border-b-primary w-full group ',
                        )}
                        value={item.name}
                      >
                        <AccordionTrigger
                          className={cn(
                            'flex items-center gap-0 px-4 py-3  font-medium transition-all [&[data-state=open]>svg]:rotate-180  w-full group-hover:bg-gray-100 group-hover:text-primary',
                          )}
                        >
                          <Link
                            href={item.href}
                            className="w-full flex items-center justify-start"
                            onClick={() => {
                              setMobileMenuOpen(false);
                            }}
                          >
                            {item.name}
                          </Link>
                        </AccordionTrigger>
                      </AccordionItem>
                    );
                  }
                })}

                <AccordionItem className={cn(' w-full group ')} value="item-5">
                  <AccordionTrigger
                    onClick={() =>
                      switchLanguage(locale === 'en' ? 'fr' : 'en')
                    }
                    className={cn(
                      'flex items-stretch gap-0 px-4 py-3 font-medium transition-all  w-full group-hover:bg-gray-100 group-hover:text-primary',
                    )}
                  >
                    {locale === 'fr' ? 'English' : 'Français'}
                  </AccordionTrigger>
                </AccordionItem>
              </Accordion>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </>
  );
}

export default AppHeader;
