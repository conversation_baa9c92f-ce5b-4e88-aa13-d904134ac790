{"name": "ma<PERSON>an", "version": "0.1.0", "private": true, "browserslist": [">0.3%", "not dead", "not op_mini all"], "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "deploy": "next build && npx pm2 startOrRestart ecosystem.config.js", "lint": "next lint", "fix": "next lint --fix", "prettier-check": "prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "prettier-format": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "build-storybook": "storybook build"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@popperjs/core": "^2.11.8", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-toolbar": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@react-google-maps/api": "^2.19.3", "@tabler/icons-react": "^3.17.0", "@tanstack/react-query": "^5.24.8", "@tanstack/react-query-devtools": "^5.24.8", "@tanstack/react-query-next-experimental": "^5.24.8", "@types/google.maps": "^3.58.1", "@udecode/cn": "^33.0.0", "@udecode/plate-alignment": "^34.0.0", "@udecode/plate-autoformat": "^34.0.0", "@udecode/plate-basic-elements": "^35.0.0", "@udecode/plate-basic-marks": "^34.0.0", "@udecode/plate-block-quote": "^34.0.0", "@udecode/plate-break": "^34.0.0", "@udecode/plate-caption": "^34.1.0", "@udecode/plate-code-block": "^35.0.0", "@udecode/plate-combobox": "^34.0.8", "@udecode/plate-comments": "^34.0.0", "@udecode/plate-common": "^34.0.5", "@udecode/plate-cursor": "^34.0.0", "@udecode/plate-dnd": "^34.0.0", "@udecode/plate-emoji": "^34.0.8", "@udecode/plate-excalidraw": "^34.0.0", "@udecode/plate-floating": "^34.1.1", "@udecode/plate-font": "^34.0.0", "@udecode/plate-heading": "^34.0.7", "@udecode/plate-highlight": "^34.0.0", "@udecode/plate-horizontal-rule": "^34.0.0", "@udecode/plate-indent": "^34.0.0", "@udecode/plate-indent-list": "^34.0.0", "@udecode/plate-juice": "^34.0.0", "@udecode/plate-kbd": "^34.0.0", "@udecode/plate-layout": "^34.0.0", "@udecode/plate-line-height": "^34.0.0", "@udecode/plate-link": "^34.1.1", "@udecode/plate-list": "^34.0.0", "@udecode/plate-media": "^34.1.0", "@udecode/plate-mention": "^34.0.8", "@udecode/plate-node-id": "^34.0.0", "@udecode/plate-paragraph": "^34.0.0", "@udecode/plate-reset-node": "^34.0.0", "@udecode/plate-resizable": "^34.0.0", "@udecode/plate-select": "^34.0.0", "@udecode/plate-selection": "^34.1.0", "@udecode/plate-serializer-csv": "^35.1.0", "@udecode/plate-serializer-docx": "^35.1.0", "@udecode/plate-serializer-html": "^34.0.0", "@udecode/plate-serializer-md": "^34.0.0", "@udecode/plate-tabbable": "^34.0.0", "@udecode/plate-table": "^35.1.0", "@udecode/plate-toggle": "^34.0.0", "@udecode/plate-trailing-block": "^34.0.0", "add-to-calendar-button-react": "^2.7.3", "axios": "^1.6.7", "chalk": "^5.3.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.0", "country-state-city": "^3.2.1", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "embla-carousel": "^8.1.8", "embla-carousel-autoplay": "^8.1.8", "embla-carousel-fade": "^8.1.8", "embla-carousel-react": "^8.1.8", "framer-motion": "^11.2.0", "js-cookie": "^3.0.5", "lenis": "^1.1.13", "lru-cache": "^10.2.0", "lucide-react": "^0.331.0", "mustache": "^4.2.0", "nanoid": "^5.0.7", "next": "14.1.0", "next-intl": "3.9.4", "next-themes": "^0.2.1", "nodemailer": "^6.9.13", "react": "^18.2.0", "react-day-picker": "^9.3.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-google-recaptcha-v3": "^1.10.1", "react-hook-form": "^7.50.1", "react-icons": "^5.2.0", "react-lite-youtube-embed": "^2.4.0", "react-phone-input-2": "^2.15.1", "react-responsive": "^10.0.0", "react-tweet": "^3.2.1", "sass": "^1.71.0", "sharp": "^0.33.2", "slate": "^0.103.0", "slate-history": "^0.100.0", "slate-hyperscript": "^0.100.0", "slate-react": "^0.106.0", "sonner": "^1.4.41", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.5.1"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@types/debug": "^4.1.12", "@types/js-cookie": "^3.0.6", "@types/mustache": "^4.2.5", "@types/node": "^20.11.19", "@types/nodemailer": "^6.4.15", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/scheduler": "^0.16.8", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-next": "14.1.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.5.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.31.11", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^3.1.0", "postcss": "^8.4.35", "prettier": "^3.2.5", "prettier-eslint": "^16.3.0", "storybook": "^8.0.9", "tailwindcss": "^3.4.1", "typescript": "^5.4.2"}}