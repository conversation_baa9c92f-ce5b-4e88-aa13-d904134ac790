'use client';
import Image from 'next/image';
import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';

export default function TradeShowDisplaySection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        duration: 0.6,
      },
    },
  };

  const imageVariants = {
    hidden: { opacity: 0, x: -50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.8,
        ease: 'easeOut',
      },
    },
  };

  const contentVariants = {
    hidden: { opacity: 0, x: 50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.8,
        ease: 'easeOut',
      },
    },
  };

  return (
    <motion.section
      ref={ref}
      className="w-full container mx-auto  "
      variants={containerVariants}
      initial="hidden"
      animate={isInView ? 'visible' : 'hidden'}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 items-center gap-8 lg:gap-12">
        {/* Image Section */}
        <motion.div className="w-full" variants={imageVariants}>
          <div className="relative aspect-[4/3] w-full rounded-lg overflow-hidden">
            <Image
              src="/about-us/section-1-image.jpg"
              alt="Trade show display booth with green and blue branding"
              fill
              className="object-cover"
              sizes="(max-width: 1024px) 100vw, 50vw"
            />
          </div>
        </motion.div>

        {/* Content Section */}
        <motion.div className=" space-y-4" variants={contentVariants}>
          <h1 className="text-2xl font-semibold leading-tight text-brand-primary">
            <span>
              Stand out from the crowd with innovative trade show displays in
              Vancouver, Alberta and throughout Canada by teaming up with
              Goodkey Show Services Ltd.
            </span>
          </h1>

          <div className="space-y-4 text-gray-800 leading-relaxed">
            <p className="text-lg">
              We have the capability, equipment, knowledge and expertise to meet
              any and all of your special event and trade and consumer exhibit
              requirements.
            </p>

            <p>
              As a member of OCTANORM Service Partner International (OSPI), we
              have access to an incredible inventory of components and the
              exclusive OCTACAD design software. Through this partnership and
              our own dedication to superior results, we have produced some of
              the largest tradewall tradeshows in Western Canada. You can rest
              assured that your tradeshow display will be designed and
              constructed using only the best materials and to meet your exact
              budget. We are proud to be on the industry's cutting edge and look
              forward to exceeding your expectations.
            </p>
          </div>
        </motion.div>
      </div>
    </motion.section>
  );
}
