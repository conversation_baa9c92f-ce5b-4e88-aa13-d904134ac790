export const template = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f6f9fc; color: #333;">
    <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
        <tr>
            <td align="center" style="padding: 40px 0;">
                <table role="presentation" width="600" cellspacing="0" cellpadding="0" border="0" style="background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <!-- Header -->
                    <tr>
                        <td style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px; text-align: center;">
                            <h1 style="color: #ffffff; font-size: 28px; margin: 0; font-weight: 300; text-transform: uppercase; letter-spacing: 2px;">New Submission</h1>
                            <p style="color: #ffffff; font-size: 16px; margin: 10px 0 0; opacity: 0.8;">From: {{fromType}}</p>
                        </td>
                    </tr>
                    
                    <!-- Content -->
                    <tr>
                        <td style="padding: 40px 60px;">
                            <!-- Subject -->
                            <h2 style="font-size: 24px; color: #333; margin: 0 0 20px; border-bottom: 2px solid #667eea; padding-bottom: 10px;">Subject</h2>
                            <p style="font-size: 18px; line-height: 1.6; margin: 0 0 30px; color: #555; background-color: #f8fafc; padding: 15px; border-radius: 5px;">{{subject}}</p>
                            
                            <!-- Client Information -->
                            <h2 style="font-size: 24px; color: #333; margin: 40px 0 20px; border-bottom: 2px solid #667eea; padding-bottom: 10px;">Client Information</h2>
                            <table width="100%" cellspacing="0" cellpadding="0" border="0" style="margin-bottom: 30px;">
                                <tr>
                                    <td width="30%" style="padding: 12px 0;"><strong style="color: #667eea;">Name:</strong></td>
                                    <td width="70%" style="padding: 12px 0;">{{name}}</td>
                                </tr>
                                <tr>
                                    <td width="30%" style="padding: 12px 0;"><strong style="color: #667eea;">Email:</strong></td>
                                    <td width="70%" style="padding: 12px 0;">{{email}}</td>
                                </tr>
                                <tr>
                                    <td width="30%" style="padding: 12px 0;"><strong style="color: #667eea;">Phone:</strong></td>
                                    <td width="70%" style="padding: 12px 0;">{{phoneNumber}}</td>
                                </tr>
                            </table>
                            
                            <!-- Client Message -->
                            <h2 style="font-size: 24px; color: #333; margin: 40px 0 20px; border-bottom: 2px solid #667eea; padding-bottom: 10px;">Client Message</h2>
                            <div style="background-color: #f8fafc; border-left: 4px solid #667eea; padding: 20px; margin-bottom: 30px; border-radius: 5px;">
                                <p style="font-size: 16px; line-height: 1.6; margin: 0; color: #555; font-style: italic;">{{message}}</p>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- Footer -->
           
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
`;

export const quickEmailTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body style="margin: 0; font-family: Arial, sans-serif; background-color: #f5f5f5;">

    <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
        <tr>
            <td align="center" style="padding: 50px;">
                <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
                    <tr>
                        <td align="center" style="padding: 0 50px;">
                            <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="background: white; box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.1);">
                                <tr>
                                    <td style="padding: 50px 70px;">
                                        <span style="font-weight: 600; font-size: 22px; display: block; margin-bottom: 20px; color: #333;">New Form Submission - {{fromType}}</span>

                                        <!-- Client Information -->
                                        <p style="margin-bottom: 15px;"><strong style="color: #555;">Client Information:</strong></p>
                                        <ul style="margin-bottom: 30px;">
                                            <li><strong>First Name:</strong> {{firstName}}</li>
                                            <li><strong>Last Name:</strong> {{lastName}}</li>
                                            <li><strong>Email:</strong> {{email}}</li>                                        
                                        </ul>

                                        <!-- Client Message -->
                                        <p style="margin-bottom: 15px;"><strong style="color: #555;">Client Message:</strong></p>
                                        <blockquote style="border-left: 4px solid #3498db; background-color: #ecf0f1; padding: 15px; margin: 0 0 30px 0; color: #555; font-style: italic;">
                                            <p style="margin: 0;">{{message}}</p>
                                        </blockquote>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

</body>
</html>
`;
