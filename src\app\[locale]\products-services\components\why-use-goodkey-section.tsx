'use client';
import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';
import { FaArrowRight } from 'react-icons/fa';

export default function WhyUseGoodkeySection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        duration: 0.6,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <motion.section
      ref={ref}
      className="w-full py-16 container mx-auto "
      variants={containerVariants}
      initial="hidden"
      animate={isInView ? 'visible' : 'hidden'}
    >
      <div className="space-y-12">
        <motion.div variants={itemVariants} className="space-y-6">
          <h2 className="text-2xl lg:text-3xl font-semibold text-brand-primary">
            Why Use Trade Show Displays from Goodkey?
          </h2>

          <div className="text-gray-800 leading-relaxed text-sm space-y-4">
            <p>
              Trade show displays from Goodkey Show Services Ltd. are a good
              idea for your business. With leading software, we are able to
              fully customize a unique booth design in a quick and
              cost-effective manner. Along with our software capabilities, we
              use the highest quality materials featuring countless aluminum
              profiles and frames. If you have a design in mind or need us to
              create an exhibition booth to meet your specific space
              requirements, you can count on our skill and industry expertise.
              Your options are virtually limitless. We encourage you to view our
              gallery and explore some of the distinctive trade show displays
              we've created.
            </p>
          </div>
        </motion.div>

        <motion.div
          variants={itemVariants}
          className="space-y-6 relative group"
        >
          <motion.div
            className="absolute left-[-40px] top-2"
            animate={{
              x: [0, 4, 0],
            }}
            transition={{
              duration: 2,
              ease: 'easeInOut',
              repeat: Infinity,
              repeatDelay: 1,
            }}
            whileHover={{
              scale: 1.2,
              x: 6,
              transition: { duration: 0.3 },
            }}
          >
            <FaArrowRight className="text-brand-primary w-6 h-6 flex-shrink-0 drop-shadow-sm group-hover:drop-shadow-lg transition-all duration-300 cursor-pointer" />
          </motion.div>
          <h2 className="text-2xl lg:text-3xl font-semibold text-brand-primary">
            Call us for Exhibition Booth Design in Edmonton
          </h2>

          <div className="text-gray-800 leading-relaxed text-sm space-y-4">
            <p>
              Are you looking for a smaller, two-wall display or a large, custom
              room to display your products and information? When it comes to
              exhibition booth design in Edmonton and throughout Canada, Goodkey
              Show Services Ltd. is the name you should know. If your booth is
              under 100 square feet or up to 300 square feet, we will create a
              solution that best showcases your business and what you have to
              offer. To get started on your design or to ask a question about
              our products and services, please contact us today! We are pleased
              to offer complete design, fabrication and installation services.
            </p>
          </div>
        </motion.div>
      </div>
    </motion.section>
  );
}
