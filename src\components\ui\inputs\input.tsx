import React from 'react';
import { cn } from '@/lib/utils';
import { useFormField } from '../form';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  icon?: React.ReactNode;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, icon, ...props }, ref) => {
    const { error } = useFormField();

    return (
      <div className="relative flex items-center w-full">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            {icon}
          </div>
        )}
        <input
          type={type}
          className={cn(
            'flex w-full bg-transparent text-black text-sm shadow-sm transition-colors',
            'placeholder:text-muted-foreground focus-visible:outline-none',
            'disabled:cursor-not-allowed disabled:opacity-50',
            'p-2 border border-gray-300 rounded-md',
            icon && 'pl-10',
            error && 'border-red-500',
            'hover:border-gray-400',
            'focus-visible:border-[#1a7efb] focus-visible:ring-2 focus-visible:ring-[#1a7efb] focus-visible:ring-opacity-50',
            className,
          )}
          ref={ref}
          {...props}
        />
      </div>
    );
  },
);

Input.displayName = 'Input';

export { Input };
