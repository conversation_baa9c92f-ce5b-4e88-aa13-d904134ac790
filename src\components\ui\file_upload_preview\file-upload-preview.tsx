import { useEffect, useState } from 'react';

interface IFileUploadPreview {
  file: File;
  onCancel?: () => void;
}

function FileUploadPreview({ file, onCancel }: IFileUploadPreview) {
  const [uploadProgress, setUploadProgress] = useState(0);

  useEffect(() => {
    const fileSize = file.size;
    const simulateProgress = () => {
      let progress = 0;
      const interval = setInterval(() => {
        progress += 25; // Adjust the increment to suit your needs
        if (progress >= 100) {
          clearInterval(interval);
        }
        setUploadProgress(progress);
      }, 200); // Adjust the interval to suit your needs
    };

    // Start simulating progress when a file is selected
    if (fileSize > 0) {
      simulateProgress();
    }

    return () => {
      setUploadProgress(0);
    };
  }, [file]);

  const formatFileSize = (size: number): string => {
    if (size === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(size) / Math.log(k));
    return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  return (
    <div className="flex items-center justify-between w-full border rounded-sm relative overflow-hidden ">
      <div className="flex items-center  w-full">
        <div className="self-stretch w-[70px] bg-gray-200 flex items-center justify-center ">
          {file.type.startsWith('image/') ? (
            <img
              className="object-cover w-full h-full"
              src={URL.createObjectURL(file)}
              alt="Uploaded File"
            />
          ) : (
            <span className="text-[20px]">
              {file.name.split('.').pop()?.toUpperCase()}
            </span>
          )}
        </div>
        <div className="flex flex-col gap-1 w-full py-2 px-4 ">
          <span className="font-medium text-sm  truncate w-[90%] ">
            {file.name}
          </span>
          <div className="w-full h-2 bg-blue-200 rounded-full relative">
            <div className="h-full bg-blue-500 rounded-full w-full "></div>
          </div>
          <div className="flex flex-row gap-3">
            <span className="text-[11px] font-medium ">
              {uploadProgress}% Completed
            </span>
            <span className="text-[11px] font-medium ">
              {formatFileSize(file.size)}
            </span>
          </div>
        </div>
      </div>
      <button
        className="text-xs  text-red-500 hover:text-red-700 absolute right-2 top-2 "
        onClick={onCancel}
      >
        X
      </button>
    </div>
  );
}

export default FileUploadPreview;
