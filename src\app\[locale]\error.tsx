'use client';

import { Button } from '@/components/ui/button';
import { AppError } from '@/services/queries/error';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const router = useRouter();
  const t = useTranslations('Components.ErrorPage');
  const appError = AppError.parse(error.message) ?? {
    errorCode: 500,
    message: t('otherErrorsMessage'),
  };
  return (
    <div className="flex-1 items-center justify-center text-center">
      <span className="bg-gradient-to-b from-foreground to-transparent bg-clip-text text-[10rem] font-extrabold leading-none text-transparent">
        {appError?.errorCode ?? '500'}
      </span>
      <h2 className="my-2 font-heading text-2xl font-bold">
        {appError?.message ?? t('otherErrorsMessage')}
      </h2>
      <p>{appError?.errorCode == 404 ? t('notFoundMessage') : ''}</p>
      <div className="mt-8 flex justify-center gap-2">
        <Button onClick={() => router.back()}>{t('goBackText')}</Button>
        <Button onClick={() => router.push('/')}>{t('goHomeText')}</Button>
      </div>
    </div>
  );
}
