import { cn } from '@/lib/utils';
import './style/index.scss';
interface IHamburgerButton {
  onClick: () => void;
  className?: string;
  isOpen?: boolean;
}

function HamburgerButton({ onClick, className, isOpen }: IHamburgerButton) {
  return (
    <div
      id="nav-icon3"
      onClick={onClick}
      className={cn('cursor-pointer', isOpen ? 'open' : '', className)}
    >
      <span className="bg-primary"></span>
      <span className="bg-primary"></span>
      <span className="bg-primary"></span>
      <span className="bg-primary"></span>
    </div>
  );
}

export default HamburgerButton;
