'use client';
import { ReactNode } from 'react';
import ZoomText from '../zoom_text';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface IBannerText {
  text: string | ReactNode;
  textClassName?: string;
  zoomedText?: string | ReactNode;
}

function BannerText({ text, zoomedText, textClassName }: IBannerText) {
  return (
    <div className="flex flex-col items-center justify-center gap-2  xl:gap-4  z-20 w-full ">
      {typeof text === 'string' ? (
        <motion.h2
          className={cn(
            'font-libreFranklin  text-3xl lg:text-[55px] 2xl:text-[80px] text-black font-bold text-center w-full leading-[1.4]  ',
            textClassName,
          )}
          initial={{ opacity: 0, scale: 0.3 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{
            duration: 3,
            ease: 'easeInOut',
          }}
        >
          {text}
        </motion.h2>
      ) : (
        <motion.div
          className={cn('', textClassName)}
          initial={{ opacity: 0, scale: 0.3 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{
            duration: 3,
            ease: 'easeInOut',
          }}
        >
          {text}
        </motion.div>
      )}
      {zoomedText && <ZoomText text={zoomedText} />}
    </div>
  );
}

export default BannerText;
