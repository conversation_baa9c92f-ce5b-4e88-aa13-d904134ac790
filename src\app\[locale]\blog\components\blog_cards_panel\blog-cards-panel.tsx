import ArticleCard from '@/app/[locale]/blog/components/article_card';
import { Badge } from '@/components/ui/Badge';
import { ArticleBrief } from '@/models/articlesData';
import { FileX } from 'lucide-react';
import { useTranslations } from 'next-intl';

interface IBlogCardsPanel {
  title: string;
  list: ArticleBrief[];
}

function BlogCardsPanel({ list, title }: IBlogCardsPanel) {
  const t = useTranslations('Components.blogPage');
  return (
    <section className="w-full h-full  flex flex-col gap-10  ">
      {list.length > 0 ? (
        <div className=" w-full  self-center grid grid-cols-1  md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2  gap-x-8  gap-y-16  ">
          {list.map((article, index) => (
            <ArticleCard
              key={article.id}
              id={article.id}
              index={index}
              articleNumber={index + 1}
              title={article.name}
              summary={article.summary}
              updatedAt={article?.updatedAt}
              createdAt={article.createdAt}
              image={article?.image}
              slug={article.slug}
              tags={article.tags}
              // className={cn(index === 0 && 'md:col-span-2 xl:col-span-3')}
            />
          ))}
        </div>
      ) : (
        <div className="w-full  flex  items-center justify-center ">
          <Badge variant="transparent" className="py-16 text-2xl  gap-5 ">
            <FileX size={64} className="text-primary" />
            <span>{t('noArticles')}</span>
          </Badge>
        </div>
      )}
    </section>
  );
}

export default BlogCardsPanel;
