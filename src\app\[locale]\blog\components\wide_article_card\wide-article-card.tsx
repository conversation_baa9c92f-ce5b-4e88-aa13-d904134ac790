'use client';

import { ImageData } from '@/models/ImageData';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { MoveRightIcon } from 'lucide-react';
import { formatDate } from 'date-fns';
import { useTranslations } from 'next-intl';
import AppImage from '@/components/ui/app_image';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/Badge';

interface IWideArticleCard {
  id: number;
  index: number;
  articleNumber: number;
  title: string;
  summary?: string;
  updatedAt?: Date;
  createdAt: Date;
  image?: ImageData;
  slug: string;
  tags?: string;
  className?: string;
}

export default function WideArticleCard({
  id,
  index,
  articleNumber,
  title,
  summary,
  updatedAt,
  createdAt,
  image,
  slug,
  tags,
  className,
}: IWideArticleCard) {
  const c = useTranslations('Components.common');

  return (
    <>
      <div
        className={cn(
          'w-full bg-white transition-all duration-300 rounded-lg min-h-[300px] max-h-[300px] hidden lg:flex',
          className,
        )}
      >
        <div className="w-1/2 relative">
          <AppImage
            className="rounded-r-lg w-full h-full object-cover"
            image={image}
          />
        </div>
        <div className="flex-1 px-10 xl:px-16 flex flex-col gap-6 justify-center">
          {createdAt && (
            <p className=" font-semibold text-left text-[#6941C6] text-base ">
              {formatDate(createdAt, 'dd/MM/yyyy')}
            </p>
          )}
          <h2 className="text-3xl font-bold text-gray-900 ">{title}</h2>
          {summary && (
            <p className="font-normal text-lg text-gray-500  line-clamp-5">
              {summary}
            </p>
          )}

          <div className=" w-full flex flex-row items-center justify-between pr-4 ">
            <div className="flex flex-row gap-3 flex-wrap items-center  ">
              {tags &&
                tags.split(',').map((tag, i) => (
                  <Badge
                    key={i}
                    variant="secondary"
                    className="text-white rounded-2xl   "
                  >
                    {tag}
                  </Badge>
                ))}
            </div>
            <Link
              href={`/blog/${slug}`}
              className="flex justify-center items-center group w-fit  self-end  "
            >
              <Button
                variant={'default'}
                className="w-full gap-2 text-primary text-md font-medium hover:brightness-125 transition-all duration-300 pl-0 "
              >
                {/* {c('readMore')} */}
                <MoveRightIcon className="w-8 h-8 rotate-[-45deg]  group-hover:translate-x-[5px] group-hover:translate-y-[-5px] transition-all duration-300" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
      <div
        className={cn(
          ' w-full  bg-white  transition-all duration-300 rounded-lg  flex lg:hidden flex-col justify-between ',
          ' min-h-[450px] max-h-[450px]   lg:min-w-[400px] lg:max-w-[400px] ',
          'xl:min-w-[320px] xl:max-w-[320px] ',
          ' 2xl:min-w-[400px] 2xl:max-w-[400px]',
          className,
        )}
      >
        <div className={cn('flex gap-8  h-full  flex-col')}>
          <div
            className={cn(
              'rounded-lg    w-full relative max-h-[240px] min-h-[240px] ',
            )}
          >
            <AppImage
              className="rounded-sm w-full h-full object-cover"
              image={image}
            />
          </div>
          <div
            className={cn(' w-full h-full flex flex-col gap-3 justify-center ')}
          >
            {(createdAt || updatedAt) && (
              <p className=" font-semibold text-left text-[#6941C6] text-sm ">
                {updatedAt
                  ? formatDate(updatedAt, 'dd/MM/yyyy')
                  : formatDate(createdAt, 'dd/MM/yyyy')}
              </p>
            )}
            <h5 className=" text-2xl font-bold   text-gray-900 ">{title}</h5>

            {summary && (
              <p className=" font-normal text-gray-500   line-clamp-2 overflow-hidden text-ellipsis  max-h-[50px] h-full ">
                {summary}
              </p>
            )}

            <div className=" w-full flex flex-row items-center justify-between pr-4 ">
              <div className="flex flex-row gap-3 flex-wrap items-center  ">
                {tags &&
                  tags.split(',').map((tag, i) => (
                    <Badge
                      key={i}
                      variant="secondary"
                      className="text-white rounded-2xl  "
                    >
                      {tag}
                    </Badge>
                  ))}
              </div>
              <Link
                href={`/blog/${slug}`}
                className="flex justify-center items-center group w-fit  self-end  "
              >
                <Button
                  variant={'default'}
                  className="w-full gap-2 text-primary text-md font-medium hover:brightness-125 transition-all duration-300 pl-0 "
                >
                  {/* {c('readMore')} */}
                  <MoveRightIcon className="w-8 h-8 rotate-[-45deg]  group-hover:translate-x-[5px] group-hover:translate-y-[-5px] transition-all duration-300" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
